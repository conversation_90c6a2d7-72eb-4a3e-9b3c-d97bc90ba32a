<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white">Admin Dashboard</h1>
      <p class="text-gray-400 mt-1">System overview and management</p>
    </div>
    <div class="flex items-center space-x-4">
      <span class="text-sm text-gray-400">Welcome, <%= user.username %></span>
      <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
        <i class="ti ti-user text-white text-sm"></i>
      </div>
    </div>
  </div>
</div>

      <!-- Stats Cards -->
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <!-- Total Users -->
          <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-400 text-sm">Total Users</p>
                <p class="text-2xl font-bold text-white" data-stat="total_users"><%= stats.total_users || 0 %></p>
                <p class="text-green-400 text-sm"><%= stats.active_users || 0 %> active</p>
              </div>
              <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <i class="ti ti-users text-blue-400 text-xl"></i>
              </div>
            </div>
          </div>

          <!-- Live Streams -->
          <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-400 text-sm">Live Streams</p>
                <p class="text-2xl font-bold text-white" data-stat="live_streams"><%= stats.live_streams || 0 %></p>
                <p class="text-gray-400 text-sm"><%= stats.total_streams || 0 %> total</p>
              </div>
              <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
                <i class="ti ti-broadcast text-red-400 text-xl"></i>
              </div>
            </div>
          </div>

          <!-- Total Videos -->
          <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-400 text-sm">Total Videos</p>
                <p class="text-2xl font-bold text-white" data-stat="total_videos"><%= stats.total_videos || 0 %></p>
                <p class="text-gray-400 text-sm"><%= stats.total_storage_gb || 0 %>GB used</p>
              </div>
              <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                <i class="ti ti-video text-purple-400 text-xl"></i>
              </div>
            </div>
          </div>

          <!-- Active Subscriptions -->
          <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-400 text-sm">Subscriptions</p>
                <p class="text-2xl font-bold text-white" data-stat="active_subscriptions"><%= stats.active_subscriptions || 0 %></p>
                <p class="text-green-400 text-sm">Active</p>
              </div>
              <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                <i class="ti ti-credit-card text-green-400 text-xl"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <!-- Recent Users -->
          <div class="bg-dark-800 rounded-lg border border-gray-700">
            <div class="p-6 border-b border-gray-700">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white">Recent Users</h3>
                <a href="/admin/users" class="text-primary hover:text-primary-light text-sm">View All</a>
              </div>
            </div>
            <div class="p-6">
              <% users.slice(0, 5).forEach(function(user) { %>
                <div class="flex items-center justify-between py-3 border-b border-gray-700 last:border-b-0">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                      <i class="ti ti-user text-white text-sm"></i>
                    </div>
                    <div>
                      <p class="text-white font-medium"><%= user.username %></p>
                      <p class="text-gray-400 text-sm"><%= user.email || 'No email' %></p>
                    </div>
                  </div>
                  <div class="text-right">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= user.role === 'admin' ? 'bg-red-100 text-red-800' : user.role === 'moderator' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800' %>">
                      <%= user.role %>
                    </span>
                    <p class="text-gray-400 text-xs mt-1"><%= user.plan_type %></p>
                  </div>
                </div>
              <% }); %>
            </div>
          </div>

          <!-- Subscription Plans -->
          <div class="bg-dark-800 rounded-lg border border-gray-700">
            <div class="p-6 border-b border-gray-700">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white">Subscription Plans</h3>
                <a href="/admin/plans" class="text-primary hover:text-primary-light text-sm">Manage</a>
              </div>
            </div>
            <div class="p-6">
              <% plans.forEach(function(plan) { %>
                <div class="flex items-center justify-between py-3 border-b border-gray-700 last:border-b-0">
                  <div>
                    <p class="text-white font-medium"><%= plan.name %></p>
                    <p class="text-gray-400 text-sm"><%= plan.max_streaming_slots === -1 ? 'Unlimited' : plan.max_streaming_slots %> slots, <%= plan.max_storage_gb %>GB</p>
                  </div>
                  <div class="text-right">
                    <p class="text-white font-medium">$<%= plan.price %></p>
                    <p class="text-gray-400 text-xs"><%= plan.billing_period %></p>
                  </div>
                </div>
              <% }); %>
            </div>
          </div>
        </div>

        <!-- Quick Actions Buttons -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <a href="/admin/users" class="bg-dark-800 hover:bg-dark-700 border border-gray-700 rounded-lg p-6 text-center transition-colors">
            <i class="ti ti-users text-blue-400 text-2xl mb-3"></i>
            <h4 class="text-white font-medium mb-2">Manage Users</h4>
            <p class="text-gray-400 text-sm">View and manage user accounts</p>
          </a>

          <a href="/admin/plans" class="bg-dark-800 hover:bg-dark-700 border border-gray-700 rounded-lg p-6 text-center transition-colors">
            <i class="ti ti-credit-card text-green-400 text-2xl mb-3"></i>
            <h4 class="text-white font-medium mb-2">Subscription Plans</h4>
            <p class="text-gray-400 text-sm">Configure pricing and features</p>
          </a>

          <a href="/admin/stats" class="bg-dark-800 hover:bg-dark-700 border border-gray-700 rounded-lg p-6 text-center transition-colors">
            <i class="ti ti-chart-bar text-purple-400 text-2xl mb-3"></i>
            <h4 class="text-white font-medium mb-2">Analytics</h4>
            <p class="text-gray-400 text-sm">View detailed statistics</p>
          </a>
        </div>
      </div>
    </div>

    <script>
      // Auto-refresh stats every 30 seconds
      setInterval(async () => {
        try {
          const response = await fetch('/admin/stats');
          const stats = await response.json();

          // Update stats display
          document.querySelector('[data-stat="total_users"]').textContent = stats.total_users || 0;
          document.querySelector('[data-stat="live_streams"]').textContent = stats.live_streams || 0;
          document.querySelector('[data-stat="total_videos"]').textContent = stats.total_videos || 0;
          document.querySelector('[data-stat="active_subscriptions"]').textContent = stats.active_subscriptions || 0;
        } catch (error) {
          console.error('Failed to refresh stats:', error);
        }
      }, 30000);
    </script>
