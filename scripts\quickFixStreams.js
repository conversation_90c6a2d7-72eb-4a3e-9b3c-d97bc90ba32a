const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Connect to database
const dbPath = path.join(__dirname, '..', 'streamflow.db');
const db = new sqlite3.Database(dbPath);

console.log('Fixing streams table...');

// Add missing columns
const alterQueries = [
  'ALTER TABLE streams ADD COLUMN loop_video BOOLEAN DEFAULT 1',
  'ALTER TABLE streams ADD COLUMN orientation TEXT DEFAULT "horizontal"',
  'ALTER TABLE streams ADD COLUMN status_updated_at TIMESTAMP',
  'ALTER TABLE streams ADD COLUMN start_time TIMESTAMP',
  'ALTER TABLE streams ADD COLUMN end_time TIMESTAMP'
];

// Update queries
const updateQueries = [
  'UPDATE streams SET loop_video = 1 WHERE loop_video IS NULL',
  'UPDATE streams SET orientation = "horizontal" WHERE orientation IS NULL',
  'UPDATE streams SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL'
];

let completed = 0;
const totalQueries = alterQueries.length + updateQueries.length;

function runQuery(query, description) {
  db.run(query, function(err) {
    if (err && !err.message.includes('duplicate column name')) {
      console.error(`Error ${description}:`, err.message);
    } else {
      console.log(`✓ ${description}`);
    }
    
    completed++;
    if (completed === totalQueries) {
      console.log('✅ All fixes completed!');
      
      // Show final table structure
      db.all('PRAGMA table_info(streams)', [], (err, rows) => {
        if (err) {
          console.error('Error getting table info:', err);
        } else {
          console.log('\nFinal streams table columns:');
          rows.forEach(col => {
            console.log(`  - ${col.name}: ${col.type}`);
          });
        }
        db.close();
      });
    }
  });
}

// Run alter queries
alterQueries.forEach((query, index) => {
  runQuery(query, `Adding column ${index + 1}`);
});

// Run update queries
updateQueries.forEach((query, index) => {
  runQuery(query, `Updating records ${index + 1}`);
});
