<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="max-w-7xl mx-auto">
    <h1 class="text-3xl font-bold text-white mb-2">Choose Your Plan</h1>
    <p class="text-gray-400">Select the perfect plan for your streaming needs</p>
  </div>
</div>

<!-- Current Plan Info -->
<% if (typeof currentSubscription !== 'undefined' && currentSubscription) { %>
  <div class="bg-blue-500/10 border border-blue-500/20 rounded-lg p-6 mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold text-blue-400">Current Plan: <%= currentSubscription.plan_name %></h3>
          <% if (typeof quotaInfo !== 'undefined' && quotaInfo) { %>
            <p class="text-gray-300">
              Streaming: <%= quotaInfo.streaming.current %>/<%= quotaInfo.streaming.max %> slots used |
              Storage: <%= quotaInfo.storage.current %>GB/<%= quotaInfo.storage.max %>GB used (<%= quotaInfo.storage.percentage %>%)
            </p>
          <% } %>
        </div>
        <div class="text-right">
          <p class="text-gray-400 text-sm">Expires: <%= new Date(currentSubscription.end_date).toLocaleDateString() %></p>
          <button onclick="cancelSubscription()" class="text-red-400 hover:text-red-300 text-sm mt-1">Cancel Plan</button>
        </div>
    </div>
  </div>
<% } %>

<!-- Pricing Cards -->
<div class="max-w-7xl mx-auto px-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <% plans.forEach(function(plan) { %>
        <div class="bg-dark-800 rounded-lg border <%= plan.name === 'Pro' ? 'border-primary' : 'border-gray-700' %> relative overflow-hidden">
          <% if (plan.name === 'Pro') { %>
            <div class="absolute top-0 left-0 right-0 bg-primary text-white text-center py-2 text-sm font-medium">
              Most Popular
            </div>
          <% } %>

          <div class="p-6 <%= plan.name === 'Pro' ? 'pt-12' : '' %>">
            <!-- Plan Header -->
            <div class="text-center mb-6">
              <h3 class="text-xl font-bold text-white mb-2"><%= plan.name %></h3>
              <div class="mb-4">
                <span class="text-3xl font-bold text-white">$<%= plan.price %></span>
                <span class="text-gray-400">/<%= plan.billing_period %></span>
              </div>
            </div>

            <!-- Features -->
            <div class="space-y-3 mb-6">
              <div class="flex items-center text-sm">
                <i class="ti ti-check text-green-400 mr-2"></i>
                <span class="text-gray-300">
                  <%= plan.max_streaming_slots === -1 ? 'Unlimited' : plan.max_streaming_slots %> Streaming Slot<%= plan.max_streaming_slots !== 1 ? 's' : '' %>
                </span>
              </div>
              <div class="flex items-center text-sm">
                <i class="ti ti-check text-green-400 mr-2"></i>
                <span class="text-gray-300"><%= plan.max_storage_gb %>GB Storage</span>
              </div>
              <% if (plan.features) { %>
                <% try { %>
                  <% var features = typeof plan.features === 'string' ? JSON.parse(plan.features) : plan.features; %>
                  <% features.forEach(function(feature) { %>
                    <div class="flex items-center text-sm">
                      <i class="ti ti-check text-green-400 mr-2"></i>
                      <span class="text-gray-300"><%= feature %></span>
                    </div>
                  <% }); %>
                <% } catch(e) { %>
                  <div class="flex items-center text-sm">
                    <i class="ti ti-check text-green-400 mr-2"></i>
                    <span class="text-gray-300">Basic Features</span>
                  </div>
                <% } %>
              <% } %>
            </div>

            <!-- Action Button -->
            <div class="text-center">
              <% if (typeof currentSubscription !== 'undefined' && currentSubscription && currentSubscription.plan_name === plan.name) { %>
                <button class="w-full bg-gray-600 text-gray-300 py-2 px-4 rounded-lg cursor-not-allowed" disabled>
                  Current Plan
                </button>
              <% } else if (plan.price === 0) { %>
                <button onclick="subscribeToPlan('<%= plan.id %>')" class="w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors">
                  Get Started
                </button>
              <% } else { %>
                <button onclick="subscribeToPlan('<%= plan.id %>')" class="w-full <%= plan.name === 'Pro' ? 'bg-primary hover:bg-primary-dark' : 'bg-blue-600 hover:bg-blue-700' %> text-white py-2 px-4 rounded-lg transition-colors">
                  <%= typeof currentSubscription !== 'undefined' && currentSubscription ? 'Upgrade' : 'Subscribe' %>
                </button>
              <% } %>
            </div>
          </div>
        </div>
      <% }); %>
  </div>

  <!-- FAQ Section -->
  <div class="mt-16">
      <h2 class="text-2xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
      <div class="max-w-3xl mx-auto space-y-4">
        <div class="bg-dark-800 rounded-lg border border-gray-700">
          <button class="w-full text-left p-6 focus:outline-none" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-white">Can I change my plan anytime?</h3>
              <i class="ti ti-chevron-down text-gray-400 transform transition-transform"></i>
            </div>
          </button>
          <div class="hidden px-6 pb-6">
            <p class="text-gray-300">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
          </div>
        </div>

        <div class="bg-dark-800 rounded-lg border border-gray-700">
          <button class="w-full text-left p-6 focus:outline-none" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-white">What happens if I exceed my limits?</h3>
              <i class="ti ti-chevron-down text-gray-400 transform transition-transform"></i>
            </div>
          </button>
          <div class="hidden px-6 pb-6">
            <p class="text-gray-300">If you reach your streaming or storage limits, you'll need to upgrade your plan or free up space to continue using the service.</p>
          </div>
        </div>

        <div class="bg-dark-800 rounded-lg border border-gray-700">
          <button class="w-full text-left p-6 focus:outline-none" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-white">Is there a free trial?</h3>
              <i class="ti ti-chevron-down text-gray-400 transform transition-transform"></i>
            </div>
          </button>
          <div class="hidden px-6 pb-6">
            <p class="text-gray-300">Yes! Start with our Free plan to test the platform. No credit card required.</p>
          </div>
        </div>
    </div>
  </div>
</div>

<script>
  function toggleFAQ(button) {
    const content = button.nextElementSibling;
    const icon = button.querySelector('i');

    content.classList.toggle('hidden');
    icon.classList.toggle('rotate-180');
  }

  async function subscribeToPlan(planId) {
    try {
      const response = await fetch('/subscription/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId })
      });

      const result = await response.json();

      if (result.success) {
        alert('Successfully subscribed to plan!');
        window.location.reload();
      } else {
        alert('Error: ' + result.error);
      }
    } catch (error) {
      console.error('Subscription error:', error);
      alert('Failed to subscribe. Please try again.');
    }
  }

  async function cancelSubscription() {
    if (!confirm('Are you sure you want to cancel your subscription?')) {
      return;
    }

    try {
      const response = await fetch('/subscription/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const result = await response.json();

      if (result.success) {
        alert('Subscription cancelled successfully.');
        window.location.reload();
      } else {
        alert('Error: ' + result.error);
      }
    } catch (error) {
      console.error('Cancellation error:', error);
      alert('Failed to cancel subscription. Please try again.');
    }
  }
</script>
