const { db } = require('../db/database');

// Function to check if column exists
function columnExists(tableName, columnName) {
  return new Promise((resolve, reject) => {
    db.all(`PRAGMA table_info(${tableName})`, [], (err, rows) => {
      if (err) {
        reject(err);
      } else {
        const exists = rows.some(row => row.name === columnName);
        resolve(exists);
      }
    });
  });
}

// Function to add column if it doesn't exist
function addColumnIfNotExists(tableName, columnName, columnType) {
  return new Promise(async (resolve, reject) => {
    try {
      const exists = await columnExists(tableName, columnName);
      if (!exists) {
        db.run(`ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnType}`, (err) => {
          if (err) {
            console.error(`Error adding column ${columnName} to ${tableName}:`, err.message);
            reject(err);
          } else {
            console.log(`✓ Added column ${columnName} to ${tableName}`);
            resolve();
          }
        });
      } else {
        console.log(`✓ Column ${columnName} already exists in ${tableName}`);
        resolve();
      }
    } catch (error) {
      reject(error);
    }
  });
}

async function fixStreamsTable() {
  console.log('Fixing streams table schema...');
  
  try {
    // Check current table structure
    const tableInfo = await new Promise((resolve, reject) => {
      db.all('PRAGMA table_info(streams)', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('Current streams table columns:');
    tableInfo.forEach(col => {
      console.log(`  - ${col.name}: ${col.type} (default: ${col.dflt_value})`);
    });

    // Add missing columns
    console.log('\nAdding missing columns...');
    
    await addColumnIfNotExists('streams', 'loop_video', 'BOOLEAN DEFAULT 1');
    await addColumnIfNotExists('streams', 'orientation', 'TEXT DEFAULT "horizontal"');
    await addColumnIfNotExists('streams', 'status_updated_at', 'TIMESTAMP');
    await addColumnIfNotExists('streams', 'start_time', 'TIMESTAMP');
    await addColumnIfNotExists('streams', 'end_time', 'TIMESTAMP');
    await addColumnIfNotExists('streams', 'updated_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP');

    // Update existing records to have proper default values
    console.log('\nUpdating existing records...');
    
    await new Promise((resolve, reject) => {
      db.run(`UPDATE streams SET loop_video = 1 WHERE loop_video IS NULL`, [], function(err) {
        if (err) reject(err);
        else {
          console.log(`✓ Updated ${this.changes} streams with default loop_video value`);
          resolve();
        }
      });
    });

    await new Promise((resolve, reject) => {
      db.run(`UPDATE streams SET orientation = 'horizontal' WHERE orientation IS NULL`, [], function(err) {
        if (err) reject(err);
        else {
          console.log(`✓ Updated ${this.changes} streams with default orientation value`);
          resolve();
        }
      });
    });

    await new Promise((resolve, reject) => {
      db.run(`UPDATE streams SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL`, [], function(err) {
        if (err) reject(err);
        else {
          console.log(`✓ Updated ${this.changes} streams with updated_at timestamp`);
          resolve();
        }
      });
    });

    // Show final table structure
    const finalTableInfo = await new Promise((resolve, reject) => {
      db.all('PRAGMA table_info(streams)', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('\nFinal streams table columns:');
    finalTableInfo.forEach(col => {
      console.log(`  - ${col.name}: ${col.type} (default: ${col.dflt_value})`);
    });

    console.log('✅ Streams table fixed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing streams table:', error);
  }
}

// Run fix if this script is executed directly
if (require.main === module) {
  fixStreamsTable().then(() => {
    console.log('Fix script finished');
    process.exit(0);
  }).catch(error => {
    console.error('Fix script failed:', error);
    process.exit(1);
  });
}

module.exports = { fixStreamsTable };
