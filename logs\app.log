2025-05-30T11:03:37.641Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T11:03:45.087Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T11:03:45.090Z [LOG] Stream scheduler initialized
2025-05-30T11:03:45.091Z [LOG] Checking for scheduled streams (2025-05-30T11:03:45.091Z to 2025-05-30T11:04:45.091Z)
2025-05-30T11:03:45.173Z [LOG] StreamFlow running at:
2025-05-30T11:03:45.175Z [LOG]   http://**************:7575
2025-05-30T11:03:45.176Z [LOG]   http://************:7575
2025-05-30T11:03:45.177Z [LOG]   http://*************:7575
2025-05-30T11:03:45.178Z [LOG]   http://***********:7575
2025-05-30T11:03:45.190Z [ERROR] Error finding streams: SQLITE_ERROR: no such table: streams
2025-05-30T11:03:45.196Z [ERROR] Error checking stream durations: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,  \n' +
  '               v.bitrate AS video_bitrate,        \n' +
  '               v.fps AS video_fps                 \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  '       ORDER BY s.created_at DESC', [], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:93:10
    at new Promise (<anonymous>)
    at Stream.findAll (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:65:12)
    at checkStreamDurations (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:48:38)
    at Object.init (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:11:3)
    at Object.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\streamingService.js:475:18)
    at Module._compile (node:internal/modules/cjs/loader:1376:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T11:03:45.202Z [ERROR] Error finding streams: SQLITE_ERROR: no such table: streams
2025-05-30T11:03:45.208Z [ERROR] Error resetting stream statuses: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,  \n' +
  '               v.bitrate AS video_bitrate,        \n' +
  '               v.fps AS video_fps                 \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  '       ORDER BY s.created_at DESC', [], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:93:10
    at new Promise (<anonymous>)
    at Stream.findAll (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:65:12)
    at Server.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:1478:34)
    at Object.onceWrapper (node:events:628:28)
    at Server.emit (node:events:526:35)
    at emitListeningNT (node:net:1906:10)
    at process.processTicksAndRejections (node:internal/process/task_queues:81:21) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T11:03:45.209Z [LOG] Stream scheduler initialized
2025-05-30T11:03:45.210Z [LOG] Checking for scheduled streams (2025-05-30T11:03:45.210Z to 2025-05-30T11:04:45.210Z)
2025-05-30T11:03:45.214Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:03:45.217Z [ERROR] Error finding scheduled streams: SQLITE_ERROR: no such table: streams
2025-05-30T11:03:45.221Z [ERROR] Error checking scheduled streams: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,\n' +
  '               v.bitrate AS video_bitrate,\n' +
  '               v.fps AS video_fps  \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  "        WHERE s.status = 'scheduled'\n" +
  '        AND s.schedule_time IS NOT NULL\n' +
  '        AND s.schedule_time >= ?\n' +
  '        AND s.schedule_time <= ?\n' +
  '      ', [ '2025-05-30T11:03:45.091Z', '2025-05-30T11:04:45.091Z' ], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:244:10
    at new Promise (<anonymous>)
    at Stream.findScheduledInRange (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:225:12)
    at checkScheduledStreams (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:22:34)
    at Object.init (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:10:3)
    at Object.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\streamingService.js:475:18)
    at Module._compile (node:internal/modules/cjs/loader:1376:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T11:03:45.225Z [ERROR] Error finding scheduled streams: SQLITE_ERROR: no such table: streams
2025-05-30T11:03:45.231Z [ERROR] Error checking scheduled streams: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,\n' +
  '               v.bitrate AS video_bitrate,\n' +
  '               v.fps AS video_fps  \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  "        WHERE s.status = 'scheduled'\n" +
  '        AND s.schedule_time IS NOT NULL\n' +
  '        AND s.schedule_time >= ?\n' +
  '        AND s.schedule_time <= ?\n' +
  '      ', [ '2025-05-30T11:03:45.210Z', '2025-05-30T11:04:45.210Z' ], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:244:10
    at new Promise (<anonymous>)
    at Stream.findScheduledInRange (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:225:12)
    at checkScheduledStreams (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:22:34)
    at Object.init (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:10:3)
    at Server.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:1488:20)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T11:03:45.233Z [ERROR] Error finding streams: SQLITE_ERROR: no such table: streams
2025-05-30T11:03:45.236Z [ERROR] Error checking stream durations: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,  \n' +
  '               v.bitrate AS video_bitrate,        \n' +
  '               v.fps AS video_fps                 \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  '       ORDER BY s.created_at DESC', [], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:93:10
    at new Promise (<anonymous>)
    at Stream.findAll (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:65:12)
    at checkStreamDurations (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:48:38)
    at Object.init (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:11:3)
    at Server.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:1488:20)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T11:03:45.239Z [ERROR] Error finding streams: SQLITE_ERROR: no such table: streams
2025-05-30T11:03:45.242Z [ERROR] [StreamingService] Error syncing stream statuses: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,  \n' +
  '               v.bitrate AS video_bitrate,        \n' +
  '               v.fps AS video_fps                 \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  '       ORDER BY s.created_at DESC', [], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:93:10
    at new Promise (<anonymous>)
    at Stream.findAll (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:65:12)
    at Object.syncStreamStatuses (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\streamingService.js:361:38)
    at Server.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:1490:28)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T11:05:35.760Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T11:05:36.483Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T11:05:36.486Z [LOG] Stream scheduler initialized
2025-05-30T11:05:36.487Z [LOG] Checking for scheduled streams (2025-05-30T11:05:36.487Z to 2025-05-30T11:06:36.487Z)
2025-05-30T11:05:36.545Z [LOG] StreamFlow running at:
2025-05-30T11:05:36.546Z [LOG]   http://**************:7575
2025-05-30T11:05:36.547Z [LOG]   http://************:7575
2025-05-30T11:05:36.548Z [LOG]   http://*************:7575
2025-05-30T11:05:36.549Z [LOG]   http://***********:7575
2025-05-30T11:05:36.553Z [LOG] Stream scheduler initialized
2025-05-30T11:05:36.555Z [LOG] Checking for scheduled streams (2025-05-30T11:05:36.555Z to 2025-05-30T11:06:36.555Z)
2025-05-30T11:05:36.557Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:05:36.558Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:06:20.106Z [LOG] Validation errors: [
  {
    type: 'field',
    value: 'aufanirsad',
    msg: 'Password must contain at least one uppercase letter',
    path: 'password',
    location: 'body'
  },
  {
    type: 'field',
    value: 'aufanirsad',
    msg: 'Password must contain at least one number',
    path: 'password',
    location: 'body'
  }
]
2025-05-30T11:06:36.501Z [LOG] Checking for scheduled streams (2025-05-30T11:06:36.500Z to 2025-05-30T11:07:36.500Z)
2025-05-30T11:06:36.571Z [LOG] Checking for scheduled streams (2025-05-30T11:06:36.571Z to 2025-05-30T11:07:36.571Z)
2025-05-30T11:06:39.665Z [LOG] Validation errors: [
  {
    type: 'field',
    value: 'Aufanirsad',
    msg: 'Password must contain at least one number',
    path: 'password',
    location: 'body'
  }
]
2025-05-30T11:06:59.176Z [LOG] User created successfully with ID: 32e5937c-7ef8-4586-bbc7-4373ea66bb69
2025-05-30T11:07:36.514Z [LOG] Checking for scheduled streams (2025-05-30T11:07:36.513Z to 2025-05-30T11:08:36.513Z)
2025-05-30T11:07:36.583Z [LOG] Checking for scheduled streams (2025-05-30T11:07:36.582Z to 2025-05-30T11:08:36.582Z)
2025-05-30T11:07:47.354Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748603267119-248628093.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748603267119-248628093.mp4',
  size: 42760993
}
2025-05-30T11:08:36.517Z [LOG] Checking for scheduled streams (2025-05-30T11:08:36.516Z to 2025-05-30T11:09:36.516Z)
2025-05-30T11:08:36.590Z [LOG] Checking for scheduled streams (2025-05-30T11:08:36.589Z to 2025-05-30T11:09:36.589Z)
2025-05-30T11:09:36.531Z [LOG] Checking for scheduled streams (2025-05-30T11:09:36.531Z to 2025-05-30T11:10:36.531Z)
2025-05-30T11:09:36.590Z [LOG] Checking for scheduled streams (2025-05-30T11:09:36.590Z to 2025-05-30T11:10:36.590Z)
2025-05-30T11:10:36.506Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:10:36.509Z [LOG] [StreamingService] Found inconsistent stream 89ba35ad-7dfa-406c-989a-fd3901927a36: marked as 'live' in DB but not active in memory
2025-05-30T11:10:36.511Z [LOG] [StreamingService] Updated stream 89ba35ad-7dfa-406c-989a-fd3901927a36 status to 'offline'
2025-05-30T11:10:36.513Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:10:36.537Z [LOG] Checking for scheduled streams (2025-05-30T11:10:36.537Z to 2025-05-30T11:11:36.537Z)
2025-05-30T11:10:36.599Z [LOG] Checking for scheduled streams (2025-05-30T11:10:36.599Z to 2025-05-30T11:11:36.599Z)
2025-05-30T11:11:02.434Z [LOG] [StreamingService] Using re-encoding mode for stream 89ba35ad-7dfa-406c-989a-fd3901927a36 (video needs optimization)
2025-05-30T11:11:02.436Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748603267119-248628093.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 720x1280 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:11:36.546Z [LOG] Checking for scheduled streams (2025-05-30T11:11:36.546Z to 2025-05-30T11:12:36.546Z)
2025-05-30T11:11:36.608Z [LOG] Checking for scheduled streams (2025-05-30T11:11:36.608Z to 2025-05-30T11:12:36.608Z)
2025-05-30T11:12:36.548Z [LOG] Checking for scheduled streams (2025-05-30T11:12:36.547Z to 2025-05-30T11:13:36.547Z)
2025-05-30T11:12:36.616Z [LOG] Checking for scheduled streams (2025-05-30T11:12:36.615Z to 2025-05-30T11:13:36.615Z)
2025-05-30T11:13:36.556Z [LOG] Checking for scheduled streams (2025-05-30T11:13:36.556Z to 2025-05-30T11:14:36.556Z)
2025-05-30T11:13:36.623Z [LOG] Checking for scheduled streams (2025-05-30T11:13:36.623Z to 2025-05-30T11:14:36.623Z)
2025-05-30T11:14:36.568Z [LOG] Checking for scheduled streams (2025-05-30T11:14:36.567Z to 2025-05-30T11:15:36.567Z)
2025-05-30T11:14:36.634Z [LOG] Checking for scheduled streams (2025-05-30T11:14:36.634Z to 2025-05-30T11:15:36.634Z)
2025-05-30T11:15:24.027Z [LOG] [StreamingService] Stop request for stream 89ba35ad-7dfa-406c-989a-fd3901927a36, isActive: true
2025-05-30T11:15:24.028Z [LOG] [StreamingService] Stopping active stream 89ba35ad-7dfa-406c-989a-fd3901927a36
2025-05-30T11:15:24.053Z [LOG] [StreamingService] Stream history saved for stream 89ba35ad-7dfa-406c-989a-fd3901927a36, duration: 261s
2025-05-30T11:15:24.055Z [LOG] Reset schedule_time for stopped stream 89ba35ad-7dfa-406c-989a-fd3901927a36
2025-05-30T11:15:24.058Z [LOG] [FFMPEG_EXIT] 89ba35ad-7dfa-406c-989a-fd3901927a36: Code=null, Signal=SIGTERM
2025-05-30T11:15:24.061Z [LOG] [StreamingService] Stream 89ba35ad-7dfa-406c-989a-fd3901927a36 was manually stopped, not restarting
2025-05-30T11:15:36.510Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:15:36.514Z [LOG] [StreamingService] Found inconsistent stream 89ba35ad-7dfa-406c-989a-fd3901927a36: marked as 'live' in DB but not active in memory
2025-05-30T11:15:36.517Z [LOG] [StreamingService] Updated stream 89ba35ad-7dfa-406c-989a-fd3901927a36 status to 'offline'
2025-05-30T11:15:36.518Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:15:36.570Z [LOG] Checking for scheduled streams (2025-05-30T11:15:36.570Z to 2025-05-30T11:16:36.570Z)
2025-05-30T11:15:36.579Z [LOG] [StreamingService] Using re-encoding mode for stream 89ba35ad-7dfa-406c-989a-fd3901927a36 (video needs optimization)
2025-05-30T11:15:36.581Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748603267119-248628093.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:15:36.634Z [LOG] Checking for scheduled streams (2025-05-30T11:15:36.634Z to 2025-05-30T11:16:36.634Z)
2025-05-30T11:16:36.572Z [LOG] Checking for scheduled streams (2025-05-30T11:16:36.571Z to 2025-05-30T11:17:36.571Z)
2025-05-30T11:16:36.648Z [LOG] Checking for scheduled streams (2025-05-30T11:16:36.648Z to 2025-05-30T11:17:36.648Z)
2025-05-30T11:16:42.766Z [LOG] [StreamingService] Stop request for stream 89ba35ad-7dfa-406c-989a-fd3901927a36, isActive: true
2025-05-30T11:16:42.768Z [LOG] [StreamingService] Stopping active stream 89ba35ad-7dfa-406c-989a-fd3901927a36
2025-05-30T11:16:42.784Z [LOG] [StreamingService] Stream history saved for stream 89ba35ad-7dfa-406c-989a-fd3901927a36, duration: 66s
2025-05-30T11:16:42.785Z [LOG] Reset schedule_time for stopped stream 89ba35ad-7dfa-406c-989a-fd3901927a36
2025-05-30T11:16:42.789Z [LOG] [FFMPEG_EXIT] 89ba35ad-7dfa-406c-989a-fd3901927a36: Code=null, Signal=SIGTERM
2025-05-30T11:16:42.790Z [LOG] [StreamingService] Stream 89ba35ad-7dfa-406c-989a-fd3901927a36 was manually stopped, not restarting
2025-05-30T11:16:58.008Z [LOG] [StreamingService] Using re-encoding mode for stream 89ba35ad-7dfa-406c-989a-fd3901927a36 (video needs optimization)
2025-05-30T11:16:58.010Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748603267119-248628093.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 4000k -maxrate 4800k -bufsize 6000k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1920x1080 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:17:36.579Z [LOG] Checking for scheduled streams (2025-05-30T11:17:36.578Z to 2025-05-30T11:18:36.578Z)
2025-05-30T11:17:36.656Z [LOG] Checking for scheduled streams (2025-05-30T11:17:36.655Z to 2025-05-30T11:18:36.655Z)
2025-05-30T11:18:04.922Z [LOG] [StreamingService] Stop request for stream 89ba35ad-7dfa-406c-989a-fd3901927a36, isActive: true
2025-05-30T11:18:04.923Z [LOG] [StreamingService] Stopping active stream 89ba35ad-7dfa-406c-989a-fd3901927a36
2025-05-30T11:18:04.942Z [LOG] [StreamingService] Stream history saved for stream 89ba35ad-7dfa-406c-989a-fd3901927a36, duration: 66s
2025-05-30T11:18:04.944Z [LOG] Reset schedule_time for stopped stream 89ba35ad-7dfa-406c-989a-fd3901927a36
2025-05-30T11:18:04.947Z [LOG] [FFMPEG_EXIT] 89ba35ad-7dfa-406c-989a-fd3901927a36: Code=null, Signal=SIGTERM
2025-05-30T11:18:04.948Z [LOG] [StreamingService] Stream 89ba35ad-7dfa-406c-989a-fd3901927a36 was manually stopped, not restarting
2025-05-30T11:18:25.851Z [LOG] [StreamingService] Using re-encoding mode for stream 89ba35ad-7dfa-406c-989a-fd3901927a36 (video needs optimization)
2025-05-30T11:18:25.853Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748603267119-248628093.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 4000k -maxrate 4800k -bufsize 6000k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1920x1080 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:18:36.583Z [LOG] Checking for scheduled streams (2025-05-30T11:18:36.583Z to 2025-05-30T11:19:36.583Z)
2025-05-30T11:18:36.660Z [LOG] Checking for scheduled streams (2025-05-30T11:18:36.660Z to 2025-05-30T11:19:36.660Z)
2025-05-30T11:19:35.688Z [ERROR] ValidationError: An undefined 'request.ip' was detected. This might indicate a misconfiguration or the connection being destroyed prematurely. See https://express-rate-limit.github.io/ERR_ERL_UNDEFINED_IP_ADDRESS/ for more information.
    at Object.ip (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express-rate-limit\dist\index.cjs:146:13)
    at wrappedValidations.<computed> [as ip] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express-rate-limit\dist\index.cjs:398:22)
    at Object.keyGenerator (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express-rate-limit\dist\index.cjs:669:20)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express-rate-limit\dist\index.cjs:724:32
    at async C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express-rate-limit\dist\index.cjs:704:5 {
  code: 'ERR_ERL_UNDEFINED_IP_ADDRESS',
  help: 'https://express-rate-limit.github.io/ERR_ERL_UNDEFINED_IP_ADDRESS/'
}
2025-05-30T11:19:36.583Z [LOG] Checking for scheduled streams (2025-05-30T11:19:36.582Z to 2025-05-30T11:20:36.582Z)
2025-05-30T11:19:36.661Z [LOG] Checking for scheduled streams (2025-05-30T11:19:36.660Z to 2025-05-30T11:20:36.660Z)
2025-05-30T11:20:36.522Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:20:36.524Z [LOG] [StreamingService] Stream status sync completed. Active streams: 1
2025-05-30T11:20:36.585Z [LOG] Checking for scheduled streams (2025-05-30T11:20:36.584Z to 2025-05-30T11:21:36.584Z)
2025-05-30T11:20:36.676Z [LOG] Checking for scheduled streams (2025-05-30T11:20:36.675Z to 2025-05-30T11:21:36.675Z)
2025-05-30T11:21:36.591Z [LOG] Checking for scheduled streams (2025-05-30T11:21:36.590Z to 2025-05-30T11:22:36.590Z)
2025-05-30T11:21:36.690Z [LOG] Checking for scheduled streams (2025-05-30T11:21:36.689Z to 2025-05-30T11:22:36.689Z)
2025-05-30T11:22:36.598Z [LOG] Checking for scheduled streams (2025-05-30T11:22:36.597Z to 2025-05-30T11:23:36.597Z)
2025-05-30T11:22:36.698Z [LOG] Checking for scheduled streams (2025-05-30T11:22:36.697Z to 2025-05-30T11:23:36.697Z)
2025-05-30T11:23:36.606Z [LOG] Checking for scheduled streams (2025-05-30T11:23:36.605Z to 2025-05-30T11:24:36.605Z)
2025-05-30T11:23:36.699Z [LOG] Checking for scheduled streams (2025-05-30T11:23:36.699Z to 2025-05-30T11:24:36.699Z)
2025-05-30T11:24:36.610Z [LOG] Checking for scheduled streams (2025-05-30T11:24:36.610Z to 2025-05-30T11:25:36.610Z)
2025-05-30T11:24:36.700Z [LOG] Checking for scheduled streams (2025-05-30T11:24:36.700Z to 2025-05-30T11:25:36.700Z)
2025-05-30T11:35:05.430Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T11:35:06.293Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T11:35:06.295Z [LOG] Stream scheduler initialized
2025-05-30T11:35:06.296Z [LOG] Checking for scheduled streams (2025-05-30T11:35:06.296Z to 2025-05-30T11:36:06.296Z)
2025-05-30T11:35:06.348Z [LOG] StreamFlow running at:
2025-05-30T11:35:06.349Z [LOG]   http://**************:7575
2025-05-30T11:35:06.350Z [LOG]   http://************:7575
2025-05-30T11:35:06.351Z [LOG]   http://*************:7575
2025-05-30T11:35:06.352Z [LOG]   http://***********:7575
2025-05-30T11:35:06.353Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T11:35:06.358Z [LOG] [StreamingService] Found 1 streams marked as 'live' in database
2025-05-30T11:35:06.359Z [LOG] [StreamingService] Stream 89ba35ad-7dfa-406c-989a-fd3901927a36 has been running for 16 minutes, attempting to restore tracking...
2025-05-30T11:35:06.363Z [LOG] [StreamingService] Using re-encoding mode for stream 89ba35ad-7dfa-406c-989a-fd3901927a36 (video needs optimization)
2025-05-30T11:35:06.365Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748603267119-248628093.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 4000k -maxrate 4800k -bufsize 6000k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1920x1080 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:35:06.382Z [LOG] [StreamingService] Successfully restored tracking for stream 89ba35ad-7dfa-406c-989a-fd3901927a36
2025-05-30T11:35:06.383Z [LOG] Stream scheduler initialized
2025-05-30T11:35:06.384Z [LOG] Checking for scheduled streams (2025-05-30T11:35:06.384Z to 2025-05-30T11:36:06.384Z)
2025-05-30T11:35:06.386Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:35:06.390Z [LOG] [StreamingService] Stream status sync completed. Active streams: 1
2025-05-30T11:36:06.305Z [LOG] Checking for scheduled streams (2025-05-30T11:36:06.303Z to 2025-05-30T11:37:06.303Z)
2025-05-30T11:36:06.401Z [LOG] Checking for scheduled streams (2025-05-30T11:36:06.400Z to 2025-05-30T11:37:06.400Z)
2025-05-30T11:37:06.316Z [LOG] Checking for scheduled streams (2025-05-30T11:37:06.315Z to 2025-05-30T11:38:06.315Z)
2025-05-30T11:37:06.401Z [LOG] Checking for scheduled streams (2025-05-30T11:37:06.401Z to 2025-05-30T11:38:06.401Z)
2025-05-30T11:37:40.685Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T11:37:41.511Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T11:37:41.513Z [LOG] Stream scheduler initialized
2025-05-30T11:37:41.515Z [LOG] Checking for scheduled streams (2025-05-30T11:37:41.514Z to 2025-05-30T11:38:41.514Z)
2025-05-30T11:37:41.592Z [LOG] StreamFlow running at:
2025-05-30T11:37:41.593Z [LOG]   http://**************:7575
2025-05-30T11:37:41.594Z [LOG]   http://************:7575
2025-05-30T11:37:41.595Z [LOG]   http://*************:7575
2025-05-30T11:37:41.597Z [LOG]   http://***********:7575
2025-05-30T11:37:41.599Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T11:37:41.605Z [ERROR] Error finding scheduled streams: SQLITE_ERROR: no such table: streams
2025-05-30T11:37:41.613Z [ERROR] Error checking scheduled streams: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,\n' +
  '               v.bitrate AS video_bitrate,\n' +
  '               v.fps AS video_fps  \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  "        WHERE s.status = 'scheduled'\n" +
  '        AND s.schedule_time IS NOT NULL\n' +
  '        AND s.schedule_time >= ?\n' +
  '        AND s.schedule_time <= ?\n' +
  '      ', [ '2025-05-30T11:37:41.514Z', '2025-05-30T11:38:41.514Z' ], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:244:10
    at new Promise (<anonymous>)
    at Stream.findScheduledInRange (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:225:12)
    at checkScheduledStreams (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:22:34)
    at Object.init (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:10:3)
    at Object.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\streamingService.js:533:18)
    at Module._compile (node:internal/modules/cjs/loader:1376:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T11:37:41.644Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T11:37:41.645Z [LOG] Stream scheduler initialized
2025-05-30T11:37:41.646Z [LOG] Checking for scheduled streams (2025-05-30T11:37:41.646Z to 2025-05-30T11:38:41.646Z)
2025-05-30T11:37:41.649Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:37:41.666Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:37:49.302Z [LOG] User created successfully with ID: 1748e095-78ae-4b25-820f-4ee640e3ba74
2025-05-30T11:38:30.993Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748605110699-834158579.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748605110699-834158579.mp4',
  size: 42760993
}
2025-05-30T11:38:41.513Z [LOG] Checking for scheduled streams (2025-05-30T11:38:41.512Z to 2025-05-30T11:39:41.512Z)
2025-05-30T11:38:41.655Z [LOG] Checking for scheduled streams (2025-05-30T11:38:41.654Z to 2025-05-30T11:39:41.654Z)
2025-05-30T11:39:39.203Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T11:39:39.941Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T11:39:39.946Z [LOG] Stream scheduler initialized
2025-05-30T11:39:39.948Z [LOG] Checking for scheduled streams (2025-05-30T11:39:39.948Z to 2025-05-30T11:40:39.948Z)
2025-05-30T11:39:40.010Z [LOG] StreamFlow running at:
2025-05-30T11:39:40.011Z [LOG]   http://**************:7575
2025-05-30T11:39:40.012Z [LOG]   http://************:7575
2025-05-30T11:39:40.013Z [LOG]   http://*************:7575
2025-05-30T11:39:40.014Z [LOG]   http://***********:7575
2025-05-30T11:39:40.015Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T11:39:40.019Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T11:39:40.020Z [LOG] Stream scheduler initialized
2025-05-30T11:39:40.021Z [LOG] Checking for scheduled streams (2025-05-30T11:39:40.020Z to 2025-05-30T11:40:40.020Z)
2025-05-30T11:39:40.022Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:39:40.024Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:40:00.483Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748605200256-688601605.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748605200256-688601605.mp4',
  size: 42760993
}
2025-05-30T11:40:39.958Z [LOG] Checking for scheduled streams (2025-05-30T11:40:39.958Z to 2025-05-30T11:41:39.958Z)
2025-05-30T11:40:40.027Z [LOG] Checking for scheduled streams (2025-05-30T11:40:40.027Z to 2025-05-30T11:41:40.027Z)
2025-05-30T11:41:39.971Z [LOG] Checking for scheduled streams (2025-05-30T11:41:39.969Z to 2025-05-30T11:42:39.969Z)
2025-05-30T11:41:40.039Z [LOG] Checking for scheduled streams (2025-05-30T11:41:40.038Z to 2025-05-30T11:42:40.038Z)
2025-05-30T11:42:39.988Z [LOG] Checking for scheduled streams (2025-05-30T11:42:39.987Z to 2025-05-30T11:43:39.987Z)
2025-05-30T11:42:40.053Z [LOG] Checking for scheduled streams (2025-05-30T11:42:40.053Z to 2025-05-30T11:43:40.053Z)
2025-05-30T11:43:39.989Z [LOG] Checking for scheduled streams (2025-05-30T11:43:39.988Z to 2025-05-30T11:44:39.988Z)
2025-05-30T11:43:40.059Z [LOG] Checking for scheduled streams (2025-05-30T11:43:40.059Z to 2025-05-30T11:44:40.059Z)
2025-05-30T11:44:39.952Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:44:39.955Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T11:44:39.956Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T11:44:39.957Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:44:40.003Z [LOG] Checking for scheduled streams (2025-05-30T11:44:40.003Z to 2025-05-30T11:45:40.003Z)
2025-05-30T11:44:40.071Z [LOG] Checking for scheduled streams (2025-05-30T11:44:40.070Z to 2025-05-30T11:45:40.070Z)
2025-05-30T11:45:40.012Z [LOG] Checking for scheduled streams (2025-05-30T11:45:40.011Z to 2025-05-30T11:46:40.011Z)
2025-05-30T11:45:40.083Z [LOG] Checking for scheduled streams (2025-05-30T11:45:40.078Z to 2025-05-30T11:46:40.078Z)
2025-05-30T11:46:40.021Z [LOG] Checking for scheduled streams (2025-05-30T11:46:40.021Z to 2025-05-30T11:47:40.021Z)
2025-05-30T11:46:40.088Z [LOG] Checking for scheduled streams (2025-05-30T11:46:40.088Z to 2025-05-30T11:47:40.088Z)
2025-05-30T11:47:40.034Z [LOG] Checking for scheduled streams (2025-05-30T11:47:40.034Z to 2025-05-30T11:48:40.034Z)
2025-05-30T11:47:40.100Z [LOG] Checking for scheduled streams (2025-05-30T11:47:40.099Z to 2025-05-30T11:48:40.099Z)
2025-05-30T11:48:35.473Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T11:48:36.304Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T11:48:36.306Z [LOG] Stream scheduler initialized
2025-05-30T11:48:36.307Z [LOG] Checking for scheduled streams (2025-05-30T11:48:36.306Z to 2025-05-30T11:49:36.306Z)
2025-05-30T11:48:36.353Z [ERROR] -----------------------------------
2025-05-30T11:48:36.357Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-05-30T11:48:36.358Z [ERROR] -----------------------------------
2025-05-30T11:48:40.040Z [LOG] Checking for scheduled streams (2025-05-30T11:48:40.039Z to 2025-05-30T11:49:40.039Z)
2025-05-30T11:48:40.105Z [LOG] Checking for scheduled streams (2025-05-30T11:48:40.104Z to 2025-05-30T11:49:40.104Z)
2025-05-30T11:49:36.319Z [LOG] Checking for scheduled streams (2025-05-30T11:49:36.318Z to 2025-05-30T11:50:36.318Z)
2025-05-30T11:49:39.969Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:49:39.972Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T11:49:39.974Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T11:49:39.976Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:49:40.051Z [LOG] Checking for scheduled streams (2025-05-30T11:49:40.050Z to 2025-05-30T11:50:40.050Z)
2025-05-30T11:49:40.120Z [LOG] Checking for scheduled streams (2025-05-30T11:49:40.120Z to 2025-05-30T11:50:40.120Z)
2025-05-30T11:50:40.065Z [LOG] Checking for scheduled streams (2025-05-30T11:50:40.064Z to 2025-05-30T11:51:40.064Z)
2025-05-30T11:50:40.131Z [LOG] Checking for scheduled streams (2025-05-30T11:50:40.131Z to 2025-05-30T11:51:40.131Z)
2025-05-30T11:51:40.073Z [LOG] Checking for scheduled streams (2025-05-30T11:51:40.073Z to 2025-05-30T11:52:40.073Z)
2025-05-30T11:51:40.138Z [LOG] Checking for scheduled streams (2025-05-30T11:51:40.138Z to 2025-05-30T11:52:40.138Z)
2025-05-30T11:52:18.161Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:18.179Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:19.338Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000023916d70140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:19.343Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:19.344Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:19.345Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:22.354Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:22.367Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:23.453Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 00000226f6230140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:23.460Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:23.462Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:23.462Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:26.470Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:26.488Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:26.948Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000026a7aa20140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:26.956Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:26.957Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:26.958Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:29.970Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:29.986Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:30.502Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000025f346a0140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:30.510Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:30.511Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:30.512Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:33.518Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:33.530Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:34.628Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000017591c60140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:34.635Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:34.637Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:34.637Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:37.651Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:37.664Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:38.134Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 00000201a9280140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:38.139Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:38.140Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:38.141Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:40.084Z [LOG] Checking for scheduled streams (2025-05-30T11:52:40.084Z to 2025-05-30T11:53:40.084Z)
2025-05-30T11:52:40.139Z [LOG] Checking for scheduled streams (2025-05-30T11:52:40.138Z to 2025-05-30T11:53:40.138Z)
2025-05-30T11:52:41.152Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:41.166Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:41.648Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000020ea6110140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:41.656Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:41.657Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:41.658Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:44.668Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:44.686Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:45.153Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000029a78090140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:45.159Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:45.160Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:45.162Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:48.168Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:48.188Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:49.670Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001fb7e900140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:49.676Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:49.679Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:49.680Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:52.698Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:52.709Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:53.791Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 00000287f9d40140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:53.796Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:53.798Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:53.799Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:56.814Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:56.827Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:57.303Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000029afe850140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:57.308Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:57.309Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:57.310Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:00.317Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:00.332Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:00.821Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001b5a8960140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:00.827Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:00.828Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:00.832Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:03.848Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:03.860Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:04.338Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000021bd6e70140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:04.344Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:04.345Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:04.348Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:07.352Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:07.367Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:07.822Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001d07bf90140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:07.830Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:07.831Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:07.833Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:10.848Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:10.860Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:12.357Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 00000233d28d0140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:12.363Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:12.364Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:12.365Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:15.382Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:15.392Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:16.864Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 00000158db040140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:16.870Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:16.872Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:16.873Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:19.886Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:19.899Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:21.408Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001ff72230140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:21.416Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:21.417Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:21.418Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:24.430Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:24.441Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:24.894Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000002a4dfc00140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:24.899Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:24.900Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:24.902Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:27.912Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:27.923Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:28.403Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000020c1cd50140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:28.409Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:28.411Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:28.412Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:01.637Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T11:54:02.393Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T11:54:02.394Z [LOG] Stream scheduler initialized
2025-05-30T11:54:02.396Z [LOG] Checking for scheduled streams (2025-05-30T11:54:02.395Z to 2025-05-30T11:55:02.395Z)
2025-05-30T11:54:02.466Z [LOG] StreamFlow running at:
2025-05-30T11:54:02.467Z [LOG]   http://**************:7575
2025-05-30T11:54:02.468Z [LOG]   http://************:7575
2025-05-30T11:54:02.469Z [LOG]   http://*************:7575
2025-05-30T11:54:02.470Z [LOG]   http://***********:7575
2025-05-30T11:54:02.471Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T11:54:02.503Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 1.4236 minutes
2025-05-30T11:54:02.505Z [LOG] [StreamingService] Found 1 streams marked as 'live' in database
2025-05-30T11:54:02.506Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 was recently started (34 seconds ago), marking as offline for safety
2025-05-30T11:54:02.515Z [LOG] Stream scheduler initialized
2025-05-30T11:54:02.516Z [LOG] Checking for scheduled streams (2025-05-30T11:54:02.515Z to 2025-05-30T11:55:02.515Z)
2025-05-30T11:54:02.519Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:54:02.522Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T11:54:02.525Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T11:54:02.526Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:54:18.036Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T11:54:18.037Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T11:54:18.045Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:18.048Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:29.652Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:54:29.666Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:54:30.818Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001e4c6b60140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:54:30.825Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:54:30.826Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:30.827Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:33.840Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:54:33.855Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:54:34.949Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001f8da460140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:54:34.956Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:54:34.957Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:34.958Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:37.968Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:54:37.982Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:54:38.413Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: true
2025-05-30T11:54:38.415Z [LOG] [StreamingService] Stopping active stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:38.422Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=null, Signal=SIGTERM
2025-05-30T11:54:38.423Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 was manually stopped, not restarting
2025-05-30T11:54:38.426Z [LOG] [StreamingService] Not saving history for stream e4ea3671-551a-4a7d-af09-d049aa38c001 - duration too short (0s)
2025-05-30T11:54:38.428Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:38.430Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:00:51.060Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T12:00:51.721Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T12:00:51.724Z [LOG] Stream scheduler initialized
2025-05-30T12:00:51.725Z [LOG] Checking for scheduled streams (2025-05-30T12:00:51.724Z to 2025-05-30T12:01:51.724Z)
2025-05-30T12:00:51.786Z [LOG] StreamFlow running at:
2025-05-30T12:00:51.787Z [LOG]   http://**************:7575
2025-05-30T12:00:51.788Z [LOG]   http://************:7575
2025-05-30T12:00:51.789Z [LOG]   http://*************:7575
2025-05-30T12:00:51.790Z [LOG]   http://***********:7575
2025-05-30T12:00:51.791Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T12:00:51.825Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:00:51.827Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:00:51.829Z [LOG] [StreamingService] Found 1 streams marked as 'live' in database
2025-05-30T12:00:51.830Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 has been running for 6 minutes, attempting to restore tracking...
2025-05-30T12:00:51.862Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:00:51.888Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:00:51.891Z [LOG] [StreamingService] Successfully restored tracking for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:00:51.893Z [LOG] Stream scheduler initialized
2025-05-30T12:00:51.895Z [LOG] Checking for scheduled streams (2025-05-30T12:00:51.894Z to 2025-05-30T12:01:51.894Z)
2025-05-30T12:00:51.897Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:00:51.907Z [LOG] [StreamingService] Stream status sync completed. Active streams: 1
2025-05-30T12:00:52.432Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 00000174c26a0140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are:
2025-05-30T12:00:52.434Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:00:52.438Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:00:52.439Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:00:52.440Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:00:55.459Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:00:55.474Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:00:58.993Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000002505f320140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:00:58.998Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:00:58.999Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:00:59.000Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:02.010Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:02.036Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:03.253Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000024c352b0140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:03.260Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:03.261Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:03.262Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:06.274Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:06.289Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:06.740Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000002afefdb0140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:06.746Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:06.747Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:06.748Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:09.757Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:09.779Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:13.257Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000013c52d30140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:13.263Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:13.264Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:13.265Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:16.271Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:16.284Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:16.735Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000027727f60140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:16.742Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:16.743Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:16.744Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:19.760Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:19.771Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:20.230Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000023615e40140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:20.235Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:20.236Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:20.238Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:23.245Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:23.259Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:23.761Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000026322230140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:23.767Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:23.768Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:23.770Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:26.789Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:26.806Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:27.910Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000002998d600140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:27.915Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:27.916Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:27.917Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:30.938Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:30.955Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:31.430Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001fd55040140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:31.436Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:31.438Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:31.439Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:33.437Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:01:33.438Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T12:01:33.445Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:33.447Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:34.451Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:34.466Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:35.596Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001e16dc80140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:35.601Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:35.603Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:35.604Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:38.620Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:38.633Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:42.804Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001f915f30140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:42.810Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:42.811Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:42.812Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:44.374Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:01:44.376Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T12:01:44.385Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:44.387Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:45.820Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:45.832Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:47.345Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000028ef58b0140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:47.352Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:47.353Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:47.355Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:50.370Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:50.381Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:51.739Z [LOG] Checking for scheduled streams (2025-05-30T12:01:51.738Z to 2025-05-30T12:02:51.738Z)
2025-05-30T12:01:51.853Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001d727430140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:51.860Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:51.862Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:51.863Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:51.908Z [LOG] Checking for scheduled streams (2025-05-30T12:01:51.908Z to 2025-05-30T12:02:51.908Z)
2025-05-30T12:01:52.247Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:01:52.249Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T12:01:52.256Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:52.258Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:54.869Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:54.885Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:55.349Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001b7dff50140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:55.354Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:55.355Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:55.357Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:58.362Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:58.376Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:58.843Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001f8eb360140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:58.849Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:58.850Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:58.852Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:02:13.269Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T12:02:13.915Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T12:02:13.917Z [LOG] Stream scheduler initialized
2025-05-30T12:02:13.918Z [LOG] Checking for scheduled streams (2025-05-30T12:02:13.918Z to 2025-05-30T12:03:13.918Z)
2025-05-30T12:02:13.977Z [LOG] StreamFlow running at:
2025-05-30T12:02:13.978Z [LOG]   http://**************:7575
2025-05-30T12:02:13.978Z [LOG]   http://************:7575
2025-05-30T12:02:13.979Z [LOG]   http://*************:7575
2025-05-30T12:02:13.980Z [LOG]   http://***********:7575
2025-05-30T12:02:13.981Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T12:02:13.986Z [LOG] [StreamingService] Found 1 streams marked as 'live' in database
2025-05-30T12:02:13.987Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 was recently started (15 seconds ago), marking as offline for safety
2025-05-30T12:02:13.989Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 1.7396833333333332 minutes
2025-05-30T12:02:14.002Z [LOG] Stream scheduler initialized
2025-05-30T12:02:14.003Z [LOG] Checking for scheduled streams (2025-05-30T12:02:14.003Z to 2025-05-30T12:03:14.003Z)
2025-05-30T12:02:14.005Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:02:14.019Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T12:02:14.025Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T12:02:14.027Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:02:31.504Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:02:31.506Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T12:02:31.513Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:02:31.516Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:02:57.962Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:02:57.980Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:02:58.468Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000002247ab70140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:02:58.474Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:02:58.475Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:02:58.476Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:01.489Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:01.502Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:01.964Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000002a851100140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:01.969Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:01.971Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:01.973Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:04.989Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:05.003Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:05.487Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000002a68bd60140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:05.493Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:05.494Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:05.495Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:07.982Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:03:07.984Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T12:03:07.993Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:07.999Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:08.504Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:08.517Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:09.659Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 00000278aa420140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:09.667Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:09.671Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:09.672Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:12.689Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:12.700Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:13.346Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001b284230140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:13.352Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:13.354Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:13.355Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:13.930Z [LOG] Checking for scheduled streams (2025-05-30T12:03:13.929Z to 2025-05-30T12:04:13.929Z)
2025-05-30T12:03:14.014Z [LOG] Checking for scheduled streams (2025-05-30T12:03:14.013Z to 2025-05-30T12:04:14.013Z)
2025-05-30T12:03:14.577Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:03:14.578Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T12:03:14.586Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:14.587Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:16.365Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:16.377Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:17.840Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000013fffd40140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:17.845Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:17.846Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:17.847Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:20.864Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:20.874Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:21.332Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000028e9e620140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:21.338Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:21.338Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:21.340Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:22.031Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:03:22.033Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T12:03:22.040Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:22.042Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:24.345Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:24.360Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:24.913Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000026440630140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:24.918Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:24.919Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:24.920Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:27.931Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:27.942Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:28.418Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000013b71730140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:28.423Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:28.424Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:28.425Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:31.430Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:31.440Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:32.149Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: true
2025-05-30T12:03:32.150Z [LOG] [StreamingService] Stopping active stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:32.157Z [LOG] [StreamingService] Not saving history for stream e4ea3671-551a-4a7d-af09-d049aa38c001 - duration too short (0s)
2025-05-30T12:03:32.158Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:32.159Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:32.162Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=null, Signal=SIGTERM
2025-05-30T12:03:32.163Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 was manually stopped, not restarting
2025-05-30T12:03:52.153Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748606631922-224154124.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748606631922-224154124.mp4',
  size: 42760993
}
2025-05-30T12:03:52.296Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T12:04:13.938Z [LOG] Checking for scheduled streams (2025-05-30T12:04:13.937Z to 2025-05-30T12:05:13.937Z)
2025-05-30T12:04:13.941Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 1.2915666666666668 minutes
2025-05-30T12:04:14.025Z [LOG] Checking for scheduled streams (2025-05-30T12:04:14.025Z to 2025-05-30T12:05:14.025Z)
2025-05-30T12:04:27.760Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:04:27.761Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748606631922-224154124.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:04:27.777Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:05:13.948Z [LOG] Checking for scheduled streams (2025-05-30T12:05:13.947Z to 2025-05-30T12:06:13.947Z)
2025-05-30T12:05:14.029Z [LOG] Checking for scheduled streams (2025-05-30T12:05:14.028Z to 2025-05-30T12:06:14.028Z)
2025-05-30T12:06:13.958Z [LOG] Checking for scheduled streams (2025-05-30T12:06:13.958Z to 2025-05-30T12:07:13.958Z)
2025-05-30T12:06:14.037Z [LOG] Checking for scheduled streams (2025-05-30T12:06:14.036Z to 2025-05-30T12:07:14.036Z)
2025-05-30T12:06:27.792Z [LOG] Terminating stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minute duration
2025-05-30T12:06:27.796Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: true
2025-05-30T12:06:27.797Z [LOG] [StreamingService] Stopping active stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:06:27.817Z [LOG] [StreamingService] Stream history saved for stream e4ea3671-551a-4a7d-af09-d049aa38c001, duration: 2m 0s
2025-05-30T12:06:27.819Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:06:27.825Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=null, Signal=SIGTERM
2025-05-30T12:06:27.827Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 was manually stopped, not restarting
2025-05-30T12:07:13.941Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:07:13.943Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T12:07:13.944Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T12:07:13.945Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:07:13.969Z [LOG] Checking for scheduled streams (2025-05-30T12:07:13.969Z to 2025-05-30T12:08:13.969Z)
2025-05-30T12:07:13.972Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:07:13.973Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:07:14.044Z [LOG] Checking for scheduled streams (2025-05-30T12:07:14.043Z to 2025-05-30T12:08:14.043Z)
2025-05-30T12:07:14.046Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:07:14.047Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:07:37.239Z [LOG] [StreamingService] Video uses HEVC codec, re-encoding required for streaming compatibility
2025-05-30T12:07:37.240Z [LOG] [StreamingService] Using re-encoding mode for stream e4ea3671-551a-4a7d-af09-d049aa38c001 (video needs optimization)
2025-05-30T12:07:37.241Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748606631922-224154124.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 4000k -maxrate 4800k -bufsize 6000k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1920x1080 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:07:37.254Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:08:13.972Z [LOG] Checking for scheduled streams (2025-05-30T12:08:13.971Z to 2025-05-30T12:09:13.971Z)
2025-05-30T12:08:14.048Z [LOG] Checking for scheduled streams (2025-05-30T12:08:14.047Z to 2025-05-30T12:09:14.047Z)
2025-05-30T12:09:13.972Z [LOG] Checking for scheduled streams (2025-05-30T12:09:13.972Z to 2025-05-30T12:10:13.972Z)
2025-05-30T12:09:14.050Z [LOG] Checking for scheduled streams (2025-05-30T12:09:14.050Z to 2025-05-30T12:10:14.050Z)
2025-05-30T12:09:37.269Z [LOG] Terminating stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minute duration
2025-05-30T12:09:37.271Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: true
2025-05-30T12:09:37.272Z [LOG] [StreamingService] Stopping active stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:09:37.284Z [LOG] [StreamingService] Stream history saved for stream e4ea3671-551a-4a7d-af09-d049aa38c001, duration: 2m 0s
2025-05-30T12:09:37.285Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:09:37.291Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=null, Signal=SIGTERM
2025-05-30T12:09:37.292Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 was manually stopped, not restarting
2025-05-30T12:10:13.976Z [LOG] Checking for scheduled streams (2025-05-30T12:10:13.976Z to 2025-05-30T12:11:13.976Z)
2025-05-30T12:10:13.979Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:10:13.980Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:10:14.055Z [LOG] Checking for scheduled streams (2025-05-30T12:10:14.054Z to 2025-05-30T12:11:14.054Z)
2025-05-30T12:10:14.057Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:10:14.058Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:11:13.977Z [LOG] Checking for scheduled streams (2025-05-30T12:11:13.976Z to 2025-05-30T12:12:13.976Z)
2025-05-30T12:11:13.979Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:11:13.980Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:11:14.059Z [LOG] Checking for scheduled streams (2025-05-30T12:11:14.059Z to 2025-05-30T12:12:14.059Z)
2025-05-30T12:11:14.062Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:11:14.064Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:12:13.955Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:12:13.957Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T12:12:13.959Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T12:12:13.960Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:12:13.986Z [LOG] Checking for scheduled streams (2025-05-30T12:12:13.985Z to 2025-05-30T12:13:13.985Z)
2025-05-30T12:12:13.989Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:12:13.989Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:12:14.062Z [LOG] Checking for scheduled streams (2025-05-30T12:12:14.061Z to 2025-05-30T12:13:14.061Z)
2025-05-30T12:12:14.064Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:12:14.065Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:13:13.992Z [LOG] Checking for scheduled streams (2025-05-30T12:13:13.991Z to 2025-05-30T12:14:13.991Z)
2025-05-30T12:13:13.994Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:13:13.995Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:13:14.068Z [LOG] Checking for scheduled streams (2025-05-30T12:13:14.068Z to 2025-05-30T12:14:14.068Z)
2025-05-30T12:13:14.071Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:13:14.072Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:14:13.993Z [LOG] Checking for scheduled streams (2025-05-30T12:14:13.992Z to 2025-05-30T12:15:13.992Z)
2025-05-30T12:14:13.998Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:14:14.016Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:14:14.076Z [LOG] Checking for scheduled streams (2025-05-30T12:14:14.075Z to 2025-05-30T12:15:14.075Z)
2025-05-30T12:14:14.078Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:14:14.079Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:15:14.003Z [LOG] Checking for scheduled streams (2025-05-30T12:15:14.003Z to 2025-05-30T12:16:14.003Z)
2025-05-30T12:15:14.005Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:15:14.007Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:15:14.084Z [LOG] Checking for scheduled streams (2025-05-30T12:15:14.083Z to 2025-05-30T12:16:14.083Z)
2025-05-30T12:15:14.087Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:15:14.089Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:16:14.011Z [LOG] Checking for scheduled streams (2025-05-30T12:16:14.011Z to 2025-05-30T12:17:14.011Z)
2025-05-30T12:16:14.014Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:16:14.015Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:16:14.090Z [LOG] Checking for scheduled streams (2025-05-30T12:16:14.089Z to 2025-05-30T12:17:14.089Z)
2025-05-30T12:16:14.092Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:16:14.095Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:17:13.960Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:17:13.961Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T12:17:13.963Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T12:17:13.963Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:17:14.021Z [LOG] Checking for scheduled streams (2025-05-30T12:17:14.020Z to 2025-05-30T12:18:14.020Z)
2025-05-30T12:17:14.024Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:17:14.025Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:17:14.098Z [LOG] Checking for scheduled streams (2025-05-30T12:17:14.098Z to 2025-05-30T12:18:14.098Z)
2025-05-30T12:17:14.101Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:17:14.101Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:18:14.025Z [LOG] Checking for scheduled streams (2025-05-30T12:18:14.025Z to 2025-05-30T12:19:14.025Z)
2025-05-30T12:18:14.028Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:18:14.029Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:18:14.115Z [LOG] Checking for scheduled streams (2025-05-30T12:18:14.113Z to 2025-05-30T12:19:14.113Z)
2025-05-30T12:18:14.118Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:18:14.119Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:19:14.040Z [LOG] Checking for scheduled streams (2025-05-30T12:19:14.039Z to 2025-05-30T12:20:14.039Z)
2025-05-30T12:19:14.043Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:19:14.044Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:19:14.124Z [LOG] Checking for scheduled streams (2025-05-30T12:19:14.122Z to 2025-05-30T12:20:14.122Z)
2025-05-30T12:19:14.127Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:19:14.129Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:20:14.050Z [LOG] Checking for scheduled streams (2025-05-30T12:20:14.050Z to 2025-05-30T12:21:14.050Z)
2025-05-30T12:20:14.053Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:20:14.054Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:20:14.124Z [LOG] Checking for scheduled streams (2025-05-30T12:20:14.123Z to 2025-05-30T12:21:14.123Z)
2025-05-30T12:20:14.127Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:20:14.129Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:21:14.058Z [LOG] Checking for scheduled streams (2025-05-30T12:21:14.058Z to 2025-05-30T12:22:14.058Z)
2025-05-30T12:21:14.061Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:21:14.062Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:21:14.126Z [LOG] Checking for scheduled streams (2025-05-30T12:21:14.126Z to 2025-05-30T12:22:14.126Z)
2025-05-30T12:21:14.129Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:21:14.130Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:22:13.970Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:22:13.972Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T12:22:13.974Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T12:22:13.975Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:22:14.073Z [LOG] Checking for scheduled streams (2025-05-30T12:22:14.072Z to 2025-05-30T12:23:14.072Z)
2025-05-30T12:22:14.075Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:22:14.076Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:22:14.138Z [LOG] Checking for scheduled streams (2025-05-30T12:22:14.138Z to 2025-05-30T12:23:14.138Z)
2025-05-30T12:22:14.140Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:22:14.141Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:23:14.078Z [LOG] Checking for scheduled streams (2025-05-30T12:23:14.077Z to 2025-05-30T12:24:14.077Z)
2025-05-30T12:23:14.080Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:23:14.081Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:23:14.141Z [LOG] Checking for scheduled streams (2025-05-30T12:23:14.141Z to 2025-05-30T12:24:14.141Z)
2025-05-30T12:23:14.143Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:23:14.144Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:24:14.090Z [LOG] Checking for scheduled streams (2025-05-30T12:24:14.089Z to 2025-05-30T12:25:14.089Z)
2025-05-30T12:24:14.093Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:24:14.094Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:24:14.141Z [LOG] Checking for scheduled streams (2025-05-30T12:24:14.141Z to 2025-05-30T12:25:14.141Z)
2025-05-30T12:24:14.144Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:24:14.145Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:33:23.423Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T12:33:24.177Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T12:33:24.179Z [LOG] Stream scheduler initialized
2025-05-30T12:33:24.180Z [LOG] Checking for scheduled streams (2025-05-30T12:33:24.180Z to 2025-05-30T12:34:24.180Z)
2025-05-30T12:33:24.242Z [LOG] StreamFlow running at:
2025-05-30T12:33:24.243Z [LOG]   http://**************:7575
2025-05-30T12:33:24.244Z [LOG]   http://************:7575
2025-05-30T12:33:24.246Z [LOG]   http://*************:7575
2025-05-30T12:33:24.248Z [LOG]   http://***********:7575
2025-05-30T12:33:24.249Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T12:33:24.261Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:33:24.264Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:33:24.265Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:33:24.266Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:33:24.268Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.269Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.270Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.271Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.273Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.274Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.275Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.277Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.279Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.280Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.281Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.282Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.283Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.285Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:33:24.286Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:33:24.287Z [LOG] [StreamingService] Found 1 streams marked as 'live' in database
2025-05-30T12:33:24.288Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 has been running for 25 minutes, attempting to restore tracking...
2025-05-30T12:33:24.291Z [LOG] [StreamingService] Video uses HEVC codec, re-encoding required for streaming compatibility
2025-05-30T12:33:24.292Z [LOG] [StreamingService] Using re-encoding mode for stream e4ea3671-551a-4a7d-af09-d049aa38c001 (video needs optimization)
2025-05-30T12:33:24.294Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748606631922-224154124.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 4000k -maxrate 4800k -bufsize 6000k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1920x1080 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:33:24.308Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:33:24.309Z [LOG] [StreamingService] Successfully restored tracking for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:33:24.310Z [LOG] Stream scheduler initialized
2025-05-30T12:33:24.312Z [LOG] Checking for scheduled streams (2025-05-30T12:33:24.311Z to 2025-05-30T12:34:24.311Z)
2025-05-30T12:33:24.314Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:33:24.317Z [LOG] [StreamingService] Stream status sync completed. Active streams: 1
2025-05-30T12:37:35.614Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T12:37:36.388Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T12:37:36.389Z [LOG] Stream scheduler initialized
2025-05-30T12:37:36.391Z [LOG] Checking for scheduled streams (2025-05-30T12:37:36.390Z to 2025-05-30T12:38:36.390Z)
2025-05-30T12:37:36.492Z [LOG] StreamFlow running at:
2025-05-30T12:37:36.493Z [LOG]   http://**************:7575
2025-05-30T12:37:36.494Z [LOG]   http://************:7575
2025-05-30T12:37:36.495Z [LOG]   http://*************:7575
2025-05-30T12:37:36.496Z [LOG]   http://***********:7575
2025-05-30T12:37:36.498Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T12:37:36.508Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:37:36.509Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:37:36.510Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:37:36.512Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:37:36.514Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.518Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.522Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.524Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.525Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.527Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.529Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.532Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.533Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.535Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.537Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.541Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.544Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.548Z [ERROR] Error finding scheduled streams: SQLITE_ERROR: no such table: streams
2025-05-30T12:37:36.557Z [ERROR] Error checking scheduled streams: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,\n' +
  '               v.bitrate AS video_bitrate,\n' +
  '               v.fps AS video_fps  \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  "        WHERE s.status = 'scheduled'\n" +
  '        AND s.schedule_time IS NOT NULL\n' +
  '        AND s.schedule_time >= ?\n' +
  '        AND s.schedule_time <= ?\n' +
  '      ', [ '2025-05-30T12:37:36.390Z', '2025-05-30T12:38:36.390Z' ], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:244:10
    at new Promise (<anonymous>)
    at Stream.findScheduledInRange (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:225:12)
    at checkScheduledStreams (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:22:34)
    at Object.init (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:10:3)
    at Object.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\streamingService.js:588:18)
    at Module._compile (node:internal/modules/cjs/loader:1376:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T12:37:36.561Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T12:37:36.562Z [LOG] Stream scheduler initialized
2025-05-30T12:37:36.563Z [LOG] Checking for scheduled streams (2025-05-30T12:37:36.563Z to 2025-05-30T12:38:36.563Z)
2025-05-30T12:37:36.565Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:37:36.570Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:40:04.877Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T12:40:05.710Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T12:40:05.712Z [LOG] Stream scheduler initialized
2025-05-30T12:40:05.714Z [LOG] Checking for scheduled streams (2025-05-30T12:40:05.714Z to 2025-05-30T12:41:05.714Z)
2025-05-30T12:40:05.789Z [LOG] StreamFlow running at:
2025-05-30T12:40:05.790Z [LOG]   http://**************:7575
2025-05-30T12:40:05.791Z [LOG]   http://************:7575
2025-05-30T12:40:05.792Z [LOG]   http://*************:7575
2025-05-30T12:40:05.793Z [LOG]   http://***********:7575
2025-05-30T12:40:05.794Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T12:40:05.900Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T12:40:05.901Z [LOG] Stream scheduler initialized
2025-05-30T12:40:05.903Z [LOG] Checking for scheduled streams (2025-05-30T12:40:05.903Z to 2025-05-30T12:41:05.903Z)
2025-05-30T12:40:05.907Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:40:05.915Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:40:23.262Z [LOG] Validation errors: [
  {
    type: 'field',
    value: '',
    msg: 'Please enter a valid email address',
    path: 'email',
    location: 'body'
  }
]
2025-05-30T12:41:05.717Z [LOG] Checking for scheduled streams (2025-05-30T12:41:05.716Z to 2025-05-30T12:42:05.716Z)
2025-05-30T12:41:05.912Z [LOG] Checking for scheduled streams (2025-05-30T12:41:05.911Z to 2025-05-30T12:42:05.911Z)
2025-05-30T12:42:05.726Z [LOG] Checking for scheduled streams (2025-05-30T12:42:05.725Z to 2025-05-30T12:43:05.725Z)
2025-05-30T12:42:05.922Z [LOG] Checking for scheduled streams (2025-05-30T12:42:05.921Z to 2025-05-30T12:43:05.921Z)
2025-05-30T12:43:05.727Z [LOG] Checking for scheduled streams (2025-05-30T12:43:05.727Z to 2025-05-30T12:44:05.727Z)
2025-05-30T12:43:05.933Z [LOG] Checking for scheduled streams (2025-05-30T12:43:05.932Z to 2025-05-30T12:44:05.932Z)
2025-05-30T12:46:08.541Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T12:46:09.294Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T12:46:09.297Z [LOG] Stream scheduler initialized
2025-05-30T12:46:09.300Z [LOG] Checking for scheduled streams (2025-05-30T12:46:09.298Z to 2025-05-30T12:47:09.298Z)
2025-05-30T12:46:09.368Z [LOG] StreamFlow running at:
2025-05-30T12:46:09.369Z [LOG]   http://**************:7575
2025-05-30T12:46:09.370Z [LOG]   http://************:7575
2025-05-30T12:46:09.371Z [LOG]   http://*************:7575
2025-05-30T12:46:09.372Z [LOG]   http://***********:7575
2025-05-30T12:46:09.374Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T12:46:09.462Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T12:46:09.464Z [LOG] Stream scheduler initialized
2025-05-30T12:46:09.464Z [LOG] Checking for scheduled streams (2025-05-30T12:46:09.464Z to 2025-05-30T12:47:09.464Z)
2025-05-30T12:46:09.466Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:46:09.467Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:47:09.310Z [LOG] Checking for scheduled streams (2025-05-30T12:47:09.309Z to 2025-05-30T12:48:09.309Z)
2025-05-30T12:47:09.472Z [LOG] Checking for scheduled streams (2025-05-30T12:47:09.471Z to 2025-05-30T12:48:09.471Z)
2025-05-30T12:48:09.316Z [LOG] Checking for scheduled streams (2025-05-30T12:48:09.315Z to 2025-05-30T12:49:09.315Z)
2025-05-30T12:48:09.485Z [LOG] Checking for scheduled streams (2025-05-30T12:48:09.485Z to 2025-05-30T12:49:09.485Z)
2025-05-30T12:49:09.320Z [LOG] Checking for scheduled streams (2025-05-30T12:49:09.319Z to 2025-05-30T12:50:09.319Z)
2025-05-30T12:49:09.493Z [LOG] Checking for scheduled streams (2025-05-30T12:49:09.492Z to 2025-05-30T12:50:09.492Z)
2025-05-30T12:50:09.333Z [LOG] Checking for scheduled streams (2025-05-30T12:50:09.333Z to 2025-05-30T12:51:09.333Z)
2025-05-30T12:50:09.503Z [LOG] Checking for scheduled streams (2025-05-30T12:50:09.502Z to 2025-05-30T12:51:09.502Z)
2025-05-30T12:51:05.645Z [LOG] Validation errors: [
  {
    type: 'field',
    value: '',
    msg: 'Please enter a valid email address',
    path: 'email',
    location: 'body'
  }
]
2025-05-30T12:51:09.301Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:51:09.303Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:51:09.333Z [LOG] Checking for scheduled streams (2025-05-30T12:51:09.333Z to 2025-05-30T12:52:09.333Z)
2025-05-30T12:51:09.509Z [LOG] Checking for scheduled streams (2025-05-30T12:51:09.509Z to 2025-05-30T12:52:09.509Z)
2025-05-30T12:51:30.441Z [LOG] User created successfully with ID: 28fd3c08-af4b-4fe8-9b36-12e0089bd409
2025-05-30T12:51:36.658Z [ERROR] ReferenceError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\subscription\plans.ejs:1
 >> 1| <%- include('../layout', { 
    2|   title: 'Subscription Plans',
    3|   content: `
    4|     <div class="min-h-screen bg-dark-900 text-white">

C:\Users\<USER>\OriDrive\Desktop\streamflow\views\layout.ejs:188
    186|       </div>
    187|       <div class="p-6 pt-20 lg:pt-22 flex-1">
 >> 188|         <%- body %>
    189|       </div>
    190|       <div class="hidden lg:flex justify-end pr-6 py-4">
    191|         <div class="flex items-center gap-2">

body is not defined
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\layout.ejs":42:17)
    at layout (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at include (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:701:39)
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\subscription\\plans.ejs":10:17)
    at plans (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
2025-05-30T12:51:42.318Z [ERROR] ReferenceError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\admin\dashboard.ejs:1
 >> 1| <%- include('../layout', { 
    2|   title: 'Admin Dashboard',
    3|   content: `
    4|     <div class="min-h-screen bg-dark-900 text-white">

C:\Users\<USER>\OriDrive\Desktop\streamflow\views\layout.ejs:188
    186|       </div>
    187|       <div class="p-6 pt-20 lg:pt-22 flex-1">
 >> 188|         <%- body %>
    189|       </div>
    190|       <div class="hidden lg:flex justify-end pr-6 py-4">
    191|         <div class="flex items-center gap-2">

body is not defined
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\layout.ejs":42:17)
    at layout (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at include (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:701:39)
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\admin\\dashboard.ejs":10:17)
    at dashboard (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
2025-05-30T12:52:09.340Z [LOG] Checking for scheduled streams (2025-05-30T12:52:09.340Z to 2025-05-30T12:53:09.340Z)
2025-05-30T12:52:09.523Z [LOG] Checking for scheduled streams (2025-05-30T12:52:09.522Z to 2025-05-30T12:53:09.522Z)
2025-05-30T12:53:09.342Z [LOG] Checking for scheduled streams (2025-05-30T12:53:09.341Z to 2025-05-30T12:54:09.341Z)
2025-05-30T12:53:09.537Z [LOG] Checking for scheduled streams (2025-05-30T12:53:09.537Z to 2025-05-30T12:54:09.537Z)
2025-05-30T12:54:09.357Z [LOG] Checking for scheduled streams (2025-05-30T12:54:09.357Z to 2025-05-30T12:55:09.357Z)
2025-05-30T12:54:09.548Z [LOG] Checking for scheduled streams (2025-05-30T12:54:09.547Z to 2025-05-30T12:55:09.547Z)
2025-05-30T12:55:09.369Z [LOG] Checking for scheduled streams (2025-05-30T12:55:09.368Z to 2025-05-30T12:56:09.368Z)
2025-05-30T12:55:09.551Z [LOG] Checking for scheduled streams (2025-05-30T12:55:09.551Z to 2025-05-30T12:56:09.551Z)
2025-05-30T12:56:09.304Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:56:09.306Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:56:09.383Z [LOG] Checking for scheduled streams (2025-05-30T12:56:09.383Z to 2025-05-30T12:57:09.383Z)
2025-05-30T12:56:09.556Z [LOG] Checking for scheduled streams (2025-05-30T12:56:09.555Z to 2025-05-30T12:57:09.555Z)
2025-05-30T12:57:09.387Z [LOG] Checking for scheduled streams (2025-05-30T12:57:09.387Z to 2025-05-30T12:58:09.387Z)
2025-05-30T12:57:09.557Z [LOG] Checking for scheduled streams (2025-05-30T12:57:09.557Z to 2025-05-30T12:58:09.557Z)
2025-05-30T12:58:09.393Z [LOG] Checking for scheduled streams (2025-05-30T12:58:09.393Z to 2025-05-30T12:59:09.393Z)
2025-05-30T12:58:09.566Z [LOG] Checking for scheduled streams (2025-05-30T12:58:09.566Z to 2025-05-30T12:59:09.566Z)
2025-05-30T12:59:09.407Z [LOG] Checking for scheduled streams (2025-05-30T12:59:09.406Z to 2025-05-30T13:00:09.406Z)
2025-05-30T12:59:09.574Z [LOG] Checking for scheduled streams (2025-05-30T12:59:09.574Z to 2025-05-30T13:00:09.574Z)
2025-05-30T13:00:09.418Z [LOG] Checking for scheduled streams (2025-05-30T13:00:09.417Z to 2025-05-30T13:01:09.417Z)
2025-05-30T13:00:09.587Z [LOG] Checking for scheduled streams (2025-05-30T13:00:09.586Z to 2025-05-30T13:01:09.586Z)
2025-05-30T13:01:09.313Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:01:09.315Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:01:09.423Z [LOG] Checking for scheduled streams (2025-05-30T13:01:09.423Z to 2025-05-30T13:02:09.423Z)
2025-05-30T13:01:09.595Z [LOG] Checking for scheduled streams (2025-05-30T13:01:09.595Z to 2025-05-30T13:02:09.595Z)
2025-05-30T13:02:09.439Z [LOG] Checking for scheduled streams (2025-05-30T13:02:09.438Z to 2025-05-30T13:03:09.438Z)
2025-05-30T13:02:09.606Z [LOG] Checking for scheduled streams (2025-05-30T13:02:09.605Z to 2025-05-30T13:03:09.605Z)
2025-05-30T13:03:09.440Z [LOG] Checking for scheduled streams (2025-05-30T13:03:09.439Z to 2025-05-30T13:04:09.439Z)
2025-05-30T13:03:09.616Z [LOG] Checking for scheduled streams (2025-05-30T13:03:09.616Z to 2025-05-30T13:04:09.616Z)
2025-05-30T13:04:09.443Z [LOG] Checking for scheduled streams (2025-05-30T13:04:09.443Z to 2025-05-30T13:05:09.443Z)
2025-05-30T13:04:09.626Z [LOG] Checking for scheduled streams (2025-05-30T13:04:09.626Z to 2025-05-30T13:05:09.626Z)
2025-05-30T13:05:09.452Z [LOG] Checking for scheduled streams (2025-05-30T13:05:09.451Z to 2025-05-30T13:06:09.451Z)
2025-05-30T13:05:09.635Z [LOG] Checking for scheduled streams (2025-05-30T13:05:09.635Z to 2025-05-30T13:06:09.635Z)
2025-05-30T13:06:09.315Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:06:09.317Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:06:09.464Z [LOG] Checking for scheduled streams (2025-05-30T13:06:09.463Z to 2025-05-30T13:07:09.463Z)
2025-05-30T13:06:09.641Z [LOG] Checking for scheduled streams (2025-05-30T13:06:09.640Z to 2025-05-30T13:07:09.640Z)
2025-05-30T13:07:09.477Z [LOG] Checking for scheduled streams (2025-05-30T13:07:09.476Z to 2025-05-30T13:08:09.476Z)
2025-05-30T13:07:09.654Z [LOG] Checking for scheduled streams (2025-05-30T13:07:09.654Z to 2025-05-30T13:08:09.654Z)
2025-05-30T13:08:09.487Z [LOG] Checking for scheduled streams (2025-05-30T13:08:09.487Z to 2025-05-30T13:09:09.487Z)
2025-05-30T13:08:09.664Z [LOG] Checking for scheduled streams (2025-05-30T13:08:09.664Z to 2025-05-30T13:09:09.664Z)
2025-05-30T13:09:09.494Z [LOG] Checking for scheduled streams (2025-05-30T13:09:09.494Z to 2025-05-30T13:10:09.494Z)
2025-05-30T13:09:09.677Z [LOG] Checking for scheduled streams (2025-05-30T13:09:09.676Z to 2025-05-30T13:10:09.676Z)
2025-05-30T13:10:09.494Z [LOG] Checking for scheduled streams (2025-05-30T13:10:09.494Z to 2025-05-30T13:11:09.494Z)
2025-05-30T13:10:09.685Z [LOG] Checking for scheduled streams (2025-05-30T13:10:09.685Z to 2025-05-30T13:11:09.685Z)
2025-05-30T13:11:09.328Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:11:09.330Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:11:09.505Z [LOG] Checking for scheduled streams (2025-05-30T13:11:09.504Z to 2025-05-30T13:12:09.504Z)
2025-05-30T13:11:09.693Z [LOG] Checking for scheduled streams (2025-05-30T13:11:09.692Z to 2025-05-30T13:12:09.692Z)
2025-05-30T13:12:09.506Z [LOG] Checking for scheduled streams (2025-05-30T13:12:09.505Z to 2025-05-30T13:13:09.505Z)
2025-05-30T13:12:09.706Z [LOG] Checking for scheduled streams (2025-05-30T13:12:09.705Z to 2025-05-30T13:13:09.705Z)
2025-05-30T13:13:09.517Z [LOG] Checking for scheduled streams (2025-05-30T13:13:09.516Z to 2025-05-30T13:14:09.516Z)
2025-05-30T13:13:09.715Z [LOG] Checking for scheduled streams (2025-05-30T13:13:09.714Z to 2025-05-30T13:14:09.714Z)
2025-05-30T13:14:09.527Z [LOG] Checking for scheduled streams (2025-05-30T13:14:09.526Z to 2025-05-30T13:15:09.526Z)
2025-05-30T13:14:09.726Z [LOG] Checking for scheduled streams (2025-05-30T13:14:09.725Z to 2025-05-30T13:15:09.725Z)
2025-05-30T13:15:09.535Z [LOG] Checking for scheduled streams (2025-05-30T13:15:09.535Z to 2025-05-30T13:16:09.535Z)
2025-05-30T13:15:09.736Z [LOG] Checking for scheduled streams (2025-05-30T13:15:09.736Z to 2025-05-30T13:16:09.736Z)
2025-05-30T13:16:09.341Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:16:09.342Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:16:09.548Z [LOG] Checking for scheduled streams (2025-05-30T13:16:09.548Z to 2025-05-30T13:17:09.548Z)
2025-05-30T13:16:09.746Z [LOG] Checking for scheduled streams (2025-05-30T13:16:09.746Z to 2025-05-30T13:17:09.746Z)
2025-05-30T13:17:09.554Z [LOG] Checking for scheduled streams (2025-05-30T13:17:09.554Z to 2025-05-30T13:18:09.554Z)
2025-05-30T13:17:09.758Z [LOG] Checking for scheduled streams (2025-05-30T13:17:09.758Z to 2025-05-30T13:18:09.758Z)
2025-05-30T13:17:27.302Z [ERROR] Error: ENOENT: no such file or directory, open 'C:\Users\<USER>\OriDrive\Desktop\streamflow\layout.ejs'
    at Object.openSync (node:fs:581:18)
    at Object.readFileSync [as fileLoader] (node:fs:457:35)
    at fileLoader (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:293:18)
    at handleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:233:16)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:16)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:353:7
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:280:5)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
2025-05-30T13:17:31.948Z [ERROR] Error: ENOENT: no such file or directory, open 'C:\Users\<USER>\OriDrive\Desktop\streamflow\layout.ejs'
    at Object.openSync (node:fs:581:18)
    at Object.readFileSync [as fileLoader] (node:fs:457:35)
    at fileLoader (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:293:18)
    at handleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:233:16)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:16)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:353:7
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:280:5)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
2025-05-30T13:17:37.474Z [ERROR] SyntaxError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\subscription\plans.ejs:67
    65|               </div>
    66|               <% if (plan.features) { %>
 >> 67|                 <% JSON.parse(plan.features).forEach(function(feature) { %>
    68|                   <div class="flex items-center text-sm">
    69|                     <i class="ti ti-check text-green-400 mr-2"></i>
    70|                     <span class="text-gray-300"><%= feature %></span>

Unexpected non-whitespace character after JSON at position 2
    at JSON.parse (<anonymous>)
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\subscription\\plans.ejs":78:13)
    at Array.forEach (<anonymous>)
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\subscription\\plans.ejs":43:14)
    at plans (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
2025-05-30T13:21:03.244Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T13:21:03.934Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T13:21:03.935Z [LOG] Stream scheduler initialized
2025-05-30T13:21:03.936Z [LOG] Checking for scheduled streams (2025-05-30T13:21:03.936Z to 2025-05-30T13:22:03.936Z)
2025-05-30T13:21:03.990Z [LOG] StreamFlow running at:
2025-05-30T13:21:03.991Z [LOG]   http://**************:7575
2025-05-30T13:21:03.991Z [LOG]   http://************:7575
2025-05-30T13:21:03.992Z [LOG]   http://*************:7575
2025-05-30T13:21:03.992Z [LOG]   http://***********:7575
2025-05-30T13:21:03.994Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T13:21:04.065Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T13:21:04.065Z [LOG] Stream scheduler initialized
2025-05-30T13:21:04.066Z [LOG] Checking for scheduled streams (2025-05-30T13:21:04.065Z to 2025-05-30T13:22:04.065Z)
2025-05-30T13:21:04.067Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:21:04.069Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:22:03.942Z [LOG] Checking for scheduled streams (2025-05-30T13:22:03.941Z to 2025-05-30T13:23:03.941Z)
2025-05-30T13:22:04.076Z [LOG] Checking for scheduled streams (2025-05-30T13:22:04.076Z to 2025-05-30T13:23:04.076Z)
2025-05-30T13:23:03.943Z [LOG] Checking for scheduled streams (2025-05-30T13:23:03.942Z to 2025-05-30T13:24:03.942Z)
2025-05-30T13:23:04.079Z [LOG] Checking for scheduled streams (2025-05-30T13:23:04.078Z to 2025-05-30T13:24:04.078Z)
2025-05-30T13:24:03.948Z [LOG] Checking for scheduled streams (2025-05-30T13:24:03.947Z to 2025-05-30T13:25:03.947Z)
2025-05-30T13:24:04.091Z [LOG] Checking for scheduled streams (2025-05-30T13:24:04.090Z to 2025-05-30T13:25:04.090Z)
2025-05-30T13:25:03.956Z [LOG] Checking for scheduled streams (2025-05-30T13:25:03.955Z to 2025-05-30T13:26:03.955Z)
2025-05-30T13:25:04.102Z [LOG] Checking for scheduled streams (2025-05-30T13:25:04.101Z to 2025-05-30T13:26:04.101Z)
2025-05-30T13:25:23.527Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748611523286-118003451.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748611523286-118003451.mp4',
  size: 42760993
}
2025-05-30T13:25:23.948Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T13:25:24.364Z [ERROR] Error creating video: SQLITE_ERROR: table videos has no column named created_at
2025-05-30T13:25:24.366Z [ERROR] Database error: [Error: SQLITE_ERROR: table videos has no column named created_at] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-05-30T13:25:24.367Z [ERROR] Upload error details: [Error: SQLITE_ERROR: table videos has no column named created_at] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-05-30T13:26:06.369Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T13:26:07.042Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T13:26:07.044Z [LOG] Stream scheduler initialized
2025-05-30T13:26:07.045Z [LOG] Checking for scheduled streams (2025-05-30T13:26:07.045Z to 2025-05-30T13:27:07.045Z)
2025-05-30T13:26:07.106Z [LOG] StreamFlow running at:
2025-05-30T13:26:07.107Z [LOG]   http://**************:7575
2025-05-30T13:26:07.107Z [LOG]   http://************:7575
2025-05-30T13:26:07.108Z [LOG]   http://*************:7575
2025-05-30T13:26:07.108Z [LOG]   http://***********:7575
2025-05-30T13:26:07.109Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T13:26:07.183Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T13:26:07.184Z [LOG] Stream scheduler initialized
2025-05-30T13:26:07.185Z [LOG] Checking for scheduled streams (2025-05-30T13:26:07.184Z to 2025-05-30T13:27:07.184Z)
2025-05-30T13:26:07.186Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:26:07.188Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:26:33.751Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748611593483-85509875.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748611593483-85509875.mp4',
  size: 42760993
}
2025-05-30T13:26:33.872Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T13:26:34.300Z [ERROR] Error creating video: SQLITE_ERROR: table videos has no column named created_at
2025-05-30T13:26:34.302Z [ERROR] Database error: [Error: SQLITE_ERROR: table videos has no column named created_at] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-05-30T13:26:34.304Z [ERROR] Upload error details: [Error: SQLITE_ERROR: table videos has no column named created_at] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-05-30T13:27:07.047Z [LOG] Checking for scheduled streams (2025-05-30T13:27:07.047Z to 2025-05-30T13:28:07.047Z)
2025-05-30T13:27:07.192Z [LOG] Checking for scheduled streams (2025-05-30T13:27:07.191Z to 2025-05-30T13:28:07.191Z)
2025-05-30T13:27:28.646Z [ERROR] Error: Failed to lookup view "admin/plans" in views directory "C:\Users\<USER>\OriDrive\Desktop\streamflow\views"
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:597:17)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:121:9
2025-05-30T13:27:45.753Z [ERROR] Error: Failed to lookup view "admin/users" in views directory "C:\Users\<USER>\OriDrive\Desktop\streamflow\views"
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:597:17)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:48:9
2025-05-30T13:28:07.060Z [LOG] Checking for scheduled streams (2025-05-30T13:28:07.060Z to 2025-05-30T13:29:07.060Z)
2025-05-30T13:28:07.203Z [LOG] Checking for scheduled streams (2025-05-30T13:28:07.202Z to 2025-05-30T13:29:07.202Z)
2025-05-30T13:29:07.061Z [LOG] Checking for scheduled streams (2025-05-30T13:29:07.060Z to 2025-05-30T13:30:07.060Z)
2025-05-30T13:29:07.206Z [LOG] Checking for scheduled streams (2025-05-30T13:29:07.206Z to 2025-05-30T13:30:07.206Z)
2025-05-30T13:30:07.074Z [LOG] Checking for scheduled streams (2025-05-30T13:30:07.074Z to 2025-05-30T13:31:07.074Z)
2025-05-30T13:30:07.215Z [LOG] Checking for scheduled streams (2025-05-30T13:30:07.215Z to 2025-05-30T13:31:07.215Z)
2025-05-30T13:30:29.715Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T13:30:30.419Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T13:30:30.421Z [LOG] Stream scheduler initialized
2025-05-30T13:30:30.422Z [LOG] Checking for scheduled streams (2025-05-30T13:30:30.422Z to 2025-05-30T13:31:30.422Z)
2025-05-30T13:30:30.435Z [LOG] Checking database schema...
2025-05-30T13:30:30.485Z [LOG] StreamFlow running at:
2025-05-30T13:30:30.485Z [LOG]   http://**************:7575
2025-05-30T13:30:30.486Z [LOG]   http://************:7575
2025-05-30T13:30:30.487Z [LOG]   http://*************:7575
2025-05-30T13:30:30.487Z [LOG]   http://***********:7575
2025-05-30T13:30:30.488Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T13:30:30.600Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T13:30:30.600Z [LOG] Stream scheduler initialized
2025-05-30T13:30:30.601Z [LOG] Checking for scheduled streams (2025-05-30T13:30:30.601Z to 2025-05-30T13:31:30.601Z)
2025-05-30T13:30:30.603Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:30:30.605Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:30:31.508Z [LOG] ✅ Database migration completed
2025-05-30T13:30:31.509Z [LOG] Database migration completed successfully
2025-05-30T13:31:18.854Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748611878594-674066304.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748611878594-674066304.mp4',
  size: 42760993
}
2025-05-30T13:31:19.082Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T13:31:30.431Z [LOG] Checking for scheduled streams (2025-05-30T13:31:30.430Z to 2025-05-30T13:32:30.430Z)
2025-05-30T13:31:30.610Z [LOG] Checking for scheduled streams (2025-05-30T13:31:30.609Z to 2025-05-30T13:32:30.609Z)
2025-05-30T13:31:44.684Z [ERROR] Error creating stream: SQLITE_ERROR: table streams has no column named orientation
2025-05-30T13:31:44.686Z [ERROR] Error creating stream: [Error: SQLITE_ERROR: table streams has no column named orientation] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-05-30T13:32:08.602Z [ERROR] Error: Failed to lookup view "admin/users" in views directory "C:\Users\<USER>\OriDrive\Desktop\streamflow\views"
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:597:17)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:48:9
2025-05-30T13:32:11.159Z [ERROR] Error: Failed to lookup view "admin/plans" in views directory "C:\Users\<USER>\OriDrive\Desktop\streamflow\views"
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:597:17)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:121:9
2025-05-30T13:32:30.443Z [LOG] Checking for scheduled streams (2025-05-30T13:32:30.443Z to 2025-05-30T13:33:30.443Z)
2025-05-30T13:32:30.618Z [LOG] Checking for scheduled streams (2025-05-30T13:32:30.618Z to 2025-05-30T13:33:30.618Z)
2025-05-30T13:33:30.447Z [LOG] Checking for scheduled streams (2025-05-30T13:33:30.447Z to 2025-05-30T13:34:30.447Z)
2025-05-30T13:33:30.631Z [LOG] Checking for scheduled streams (2025-05-30T13:33:30.631Z to 2025-05-30T13:34:30.631Z)
2025-05-30T13:36:45.390Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T13:36:46.080Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T13:36:46.082Z [LOG] Stream scheduler initialized
2025-05-30T13:36:46.083Z [LOG] Checking for scheduled streams (2025-05-30T13:36:46.083Z to 2025-05-30T13:37:46.083Z)
2025-05-30T13:36:46.100Z [LOG] Checking database schema...
2025-05-30T13:36:46.147Z [LOG] StreamFlow running at:
2025-05-30T13:36:46.147Z [LOG]   http://**************:7575
2025-05-30T13:36:46.148Z [LOG]   http://************:7575
2025-05-30T13:36:46.149Z [LOG]   http://*************:7575
2025-05-30T13:36:46.149Z [LOG]   http://***********:7575
2025-05-30T13:36:46.150Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T13:36:46.233Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T13:36:46.234Z [LOG] Stream scheduler initialized
2025-05-30T13:36:46.235Z [LOG] Checking for scheduled streams (2025-05-30T13:36:46.235Z to 2025-05-30T13:37:46.235Z)
2025-05-30T13:36:46.238Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:36:46.239Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:36:47.170Z [LOG] ✅ Database migration completed
2025-05-30T13:36:47.172Z [LOG] Database migration completed successfully
2025-05-30T13:37:46.094Z [LOG] Checking for scheduled streams (2025-05-30T13:37:46.094Z to 2025-05-30T13:38:46.094Z)
2025-05-30T13:37:46.251Z [LOG] Checking for scheduled streams (2025-05-30T13:37:46.251Z to 2025-05-30T13:38:46.251Z)
2025-05-30T13:38:26.284Z [ERROR] ReferenceError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\admin\users.ejs:25
    23|       <div>
    24|         <p class="text-gray-400 text-sm">Total Users</p>
 >> 25|         <p class="text-2xl font-bold text-white"><%= stats.total_users || 0 %></p>
    26|       </div>
    27|       <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
    28|         <i class="ti ti-users text-blue-400 text-xl"></i>

stats is not defined
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\admin\\users.ejs":13:26)
    at users (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:609:3)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:48:9
2025-05-30T13:49:50.541Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T13:49:51.235Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T13:49:51.237Z [LOG] Stream scheduler initialized
2025-05-30T13:49:51.238Z [LOG] Checking for scheduled streams (2025-05-30T13:49:51.238Z to 2025-05-30T13:50:51.238Z)
2025-05-30T13:49:51.251Z [LOG] Checking database schema...
2025-05-30T13:49:51.295Z [LOG] StreamFlow running at:
2025-05-30T13:49:51.297Z [LOG]   http://**************:7575
2025-05-30T13:49:51.298Z [LOG]   http://************:7575
2025-05-30T13:49:51.299Z [LOG]   http://*************:7575
2025-05-30T13:49:51.299Z [LOG]   http://***********:7575
2025-05-30T13:49:51.300Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T13:49:51.377Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T13:49:51.378Z [LOG] Stream scheduler initialized
2025-05-30T13:49:51.379Z [LOG] Checking for scheduled streams (2025-05-30T13:49:51.379Z to 2025-05-30T13:50:51.379Z)
2025-05-30T13:49:51.381Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:49:51.383Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:49:52.330Z [LOG] ✅ Database migration completed
2025-05-30T13:49:52.331Z [LOG] Database migration completed successfully
2025-05-30T13:50:51.264Z [LOG] Checking for scheduled streams (2025-05-30T13:50:51.264Z to 2025-05-30T13:51:51.264Z)
2025-05-30T13:50:51.398Z [LOG] Checking for scheduled streams (2025-05-30T13:50:51.398Z to 2025-05-30T13:51:51.398Z)
2025-05-30T13:51:51.275Z [LOG] Checking for scheduled streams (2025-05-30T13:51:51.275Z to 2025-05-30T13:52:51.275Z)
2025-05-30T13:51:51.403Z [LOG] Checking for scheduled streams (2025-05-30T13:51:51.402Z to 2025-05-30T13:52:51.402Z)
2025-05-30T13:52:51.280Z [LOG] Checking for scheduled streams (2025-05-30T13:52:51.279Z to 2025-05-30T13:53:51.279Z)
2025-05-30T13:52:51.405Z [LOG] Checking for scheduled streams (2025-05-30T13:52:51.404Z to 2025-05-30T13:53:51.404Z)
2025-05-30T13:53:05.389Z [ERROR] ReferenceError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\admin\users.ejs:44
    42|       <div>
    43|         <p class="text-gray-400 text-sm">Total Users</p>
 >> 44|         <p class="text-2xl font-bold text-white"><%= stats.total_users || 0 %></p>
    45|       </div>
    46|       <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
    47|         <i class="ti ti-users text-blue-400 text-xl"></i>

stats is not defined
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\admin\\users.ejs":24:26)
    at users (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:609:3)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:48:9
2025-05-30T13:53:51.293Z [LOG] Checking for scheduled streams (2025-05-30T13:53:51.292Z to 2025-05-30T13:54:51.292Z)
2025-05-30T13:53:51.414Z [LOG] Checking for scheduled streams (2025-05-30T13:53:51.414Z to 2025-05-30T13:54:51.414Z)
2025-05-30T13:54:51.253Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:54:51.254Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:54:51.295Z [LOG] Checking for scheduled streams (2025-05-30T13:54:51.295Z to 2025-05-30T13:55:51.295Z)
2025-05-30T13:54:51.414Z [LOG] Checking for scheduled streams (2025-05-30T13:54:51.414Z to 2025-05-30T13:55:51.414Z)
2025-05-30T13:55:51.301Z [LOG] Checking for scheduled streams (2025-05-30T13:55:51.301Z to 2025-05-30T13:56:51.301Z)
2025-05-30T13:55:51.417Z [LOG] Checking for scheduled streams (2025-05-30T13:55:51.417Z to 2025-05-30T13:56:51.417Z)
2025-05-30T13:56:51.310Z [LOG] Checking for scheduled streams (2025-05-30T13:56:51.310Z to 2025-05-30T13:57:51.310Z)
2025-05-30T13:56:51.424Z [LOG] Checking for scheduled streams (2025-05-30T13:56:51.424Z to 2025-05-30T13:57:51.424Z)
2025-05-30T13:57:51.325Z [LOG] Checking for scheduled streams (2025-05-30T13:57:51.324Z to 2025-05-30T13:58:51.324Z)
2025-05-30T13:57:51.437Z [LOG] Checking for scheduled streams (2025-05-30T13:57:51.436Z to 2025-05-30T13:58:51.436Z)
2025-05-30T13:58:51.334Z [LOG] Checking for scheduled streams (2025-05-30T13:58:51.334Z to 2025-05-30T13:59:51.334Z)
2025-05-30T13:58:51.448Z [LOG] Checking for scheduled streams (2025-05-30T13:58:51.447Z to 2025-05-30T13:59:51.447Z)
2025-05-30T13:59:51.267Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:59:51.269Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:59:51.341Z [LOG] Checking for scheduled streams (2025-05-30T13:59:51.341Z to 2025-05-30T14:00:51.341Z)
2025-05-30T13:59:51.458Z [LOG] Checking for scheduled streams (2025-05-30T13:59:51.457Z to 2025-05-30T14:00:51.457Z)
2025-05-30T14:01:24.662Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T14:01:25.336Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T14:01:25.338Z [LOG] Stream scheduler initialized
2025-05-30T14:01:25.339Z [LOG] Checking for scheduled streams (2025-05-30T14:01:25.339Z to 2025-05-30T14:02:25.339Z)
2025-05-30T14:01:25.350Z [LOG] Checking database schema...
2025-05-30T14:01:25.394Z [LOG] StreamFlow running at:
2025-05-30T14:01:25.395Z [LOG]   http://**************:7575
2025-05-30T14:01:25.396Z [LOG]   http://************:7575
2025-05-30T14:01:25.396Z [LOG]   http://*************:7575
2025-05-30T14:01:25.397Z [LOG]   http://***********:7575
2025-05-30T14:01:25.398Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T14:01:25.482Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T14:01:25.482Z [LOG] Stream scheduler initialized
2025-05-30T14:01:25.483Z [LOG] Checking for scheduled streams (2025-05-30T14:01:25.483Z to 2025-05-30T14:02:25.483Z)
2025-05-30T14:01:25.486Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:01:25.488Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:01:26.419Z [LOG] ✅ Database migration completed
2025-05-30T14:01:26.421Z [LOG] Database migration completed successfully
2025-05-30T14:02:25.362Z [LOG] Checking for scheduled streams (2025-05-30T14:02:25.362Z to 2025-05-30T14:03:25.362Z)
2025-05-30T14:02:25.508Z [LOG] Checking for scheduled streams (2025-05-30T14:02:25.507Z to 2025-05-30T14:03:25.507Z)
2025-05-30T14:03:25.368Z [LOG] Checking for scheduled streams (2025-05-30T14:03:25.368Z to 2025-05-30T14:04:25.368Z)
2025-05-30T14:03:25.514Z [LOG] Checking for scheduled streams (2025-05-30T14:03:25.514Z to 2025-05-30T14:04:25.514Z)
2025-05-30T14:04:25.369Z [LOG] Checking for scheduled streams (2025-05-30T14:04:25.369Z to 2025-05-30T14:05:25.369Z)
2025-05-30T14:04:25.522Z [LOG] Checking for scheduled streams (2025-05-30T14:04:25.521Z to 2025-05-30T14:05:25.521Z)
2025-05-30T14:05:25.372Z [LOG] Checking for scheduled streams (2025-05-30T14:05:25.371Z to 2025-05-30T14:06:25.371Z)
2025-05-30T14:05:25.524Z [LOG] Checking for scheduled streams (2025-05-30T14:05:25.523Z to 2025-05-30T14:06:25.523Z)
2025-05-30T14:06:25.356Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:06:25.358Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:06:25.372Z [LOG] Checking for scheduled streams (2025-05-30T14:06:25.371Z to 2025-05-30T14:07:25.371Z)
2025-05-30T14:06:25.529Z [LOG] Checking for scheduled streams (2025-05-30T14:06:25.529Z to 2025-05-30T14:07:25.529Z)
2025-05-30T14:07:25.396Z [LOG] Checking for scheduled streams (2025-05-30T14:07:25.392Z to 2025-05-30T14:08:25.392Z)
2025-05-30T14:07:25.529Z [LOG] Checking for scheduled streams (2025-05-30T14:07:25.528Z to 2025-05-30T14:08:25.528Z)
2025-05-30T14:08:25.401Z [LOG] Checking for scheduled streams (2025-05-30T14:08:25.400Z to 2025-05-30T14:09:25.400Z)
2025-05-30T14:08:25.535Z [LOG] Checking for scheduled streams (2025-05-30T14:08:25.534Z to 2025-05-30T14:09:25.534Z)
2025-05-30T14:09:25.402Z [LOG] Checking for scheduled streams (2025-05-30T14:09:25.402Z to 2025-05-30T14:10:25.402Z)
2025-05-30T14:09:25.546Z [LOG] Checking for scheduled streams (2025-05-30T14:09:25.545Z to 2025-05-30T14:10:25.545Z)
2025-05-30T14:10:25.412Z [LOG] Checking for scheduled streams (2025-05-30T14:10:25.412Z to 2025-05-30T14:11:25.412Z)
2025-05-30T14:10:25.556Z [LOG] Checking for scheduled streams (2025-05-30T14:10:25.555Z to 2025-05-30T14:11:25.555Z)
2025-05-30T14:11:25.362Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:11:25.364Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:11:25.444Z [LOG] Checking for scheduled streams (2025-05-30T14:11:25.432Z to 2025-05-30T14:12:25.432Z)
2025-05-30T14:11:25.558Z [LOG] Checking for scheduled streams (2025-05-30T14:11:25.557Z to 2025-05-30T14:12:25.557Z)
2025-05-30T14:12:52.282Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T14:12:52.998Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T14:12:53.000Z [LOG] Stream scheduler initialized
2025-05-30T14:12:53.001Z [LOG] Checking for scheduled streams (2025-05-30T14:12:53.001Z to 2025-05-30T14:13:53.001Z)
2025-05-30T14:12:53.018Z [LOG] Checking database schema...
2025-05-30T14:12:53.065Z [LOG] StreamFlow running at:
2025-05-30T14:12:53.066Z [LOG]   http://**************:7575
2025-05-30T14:12:53.066Z [LOG]   http://************:7575
2025-05-30T14:12:53.067Z [LOG]   http://*************:7575
2025-05-30T14:12:53.068Z [LOG]   http://***********:7575
2025-05-30T14:12:53.069Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T14:12:53.148Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T14:12:53.148Z [LOG] Stream scheduler initialized
2025-05-30T14:12:53.149Z [LOG] Checking for scheduled streams (2025-05-30T14:12:53.149Z to 2025-05-30T14:13:53.149Z)
2025-05-30T14:12:53.150Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:12:53.152Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:12:54.090Z [LOG] ✅ Database migration completed
2025-05-30T14:12:54.091Z [LOG] Database migration completed successfully
2025-05-30T14:13:53.003Z [LOG] Checking for scheduled streams (2025-05-30T14:13:53.002Z to 2025-05-30T14:14:53.002Z)
2025-05-30T14:13:53.162Z [LOG] Checking for scheduled streams (2025-05-30T14:13:53.162Z to 2025-05-30T14:14:53.162Z)
