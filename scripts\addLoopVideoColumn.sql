-- Add missing columns to streams table
ALTER TABLE streams ADD COLUMN loop_video BOOLEAN DEFAULT 1;
ALTER TABLE streams ADD COLUMN orientation TEXT DEFAULT 'horizontal';
ALTER TABLE streams ADD COLUMN status_updated_at TIMESTAMP;
ALTER TABLE streams ADD COLUMN start_time TIMESTAMP;
ALTER TABLE streams ADD COLUMN end_time TIMESTAMP;

-- Update existing records with default values
UPDATE streams SET loop_video = 1 WHERE loop_video IS NULL;
UPDATE streams SET orientation = 'horizontal' WHERE orientation IS NULL;
UPDATE streams SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL;
