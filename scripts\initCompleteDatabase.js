const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Connect to database
const dbPath = path.join(__dirname, '..', 'streamflow.db');
const db = new sqlite3.Database(dbPath);

console.log('Initializing complete database...');

// Function to run SQL with promise
function runSQL(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve(this);
      }
    });
  });
}

// Function to get table info
function getTableInfo(tableName) {
  return new Promise((resolve, reject) => {
    db.all(`PRAGMA table_info(${tableName})`, [], (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

async function initializeDatabase() {
  try {
    console.log('Creating all necessary tables...');

    // 1. Users table
    await runSQL(`CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      username TEXT UNIQUE NOT NULL,
      email TEXT UNIQUE,
      password TEXT NOT NULL,
      avatar_path TEXT,
      gdrive_api_key TEXT,
      role TEXT DEFAULT 'user',
      plan_type TEXT DEFAULT 'Free',
      max_streaming_slots INTEGER DEFAULT 1,
      max_storage_gb INTEGER DEFAULT 5,
      used_storage_gb REAL DEFAULT 0,
      subscription_start_date TIMESTAMP,
      subscription_end_date TIMESTAMP,
      is_active BOOLEAN DEFAULT 1,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`);
    console.log('✓ Users table created/verified');

    // 2. Videos table
    await runSQL(`CREATE TABLE IF NOT EXISTS videos (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      filepath TEXT NOT NULL,
      thumbnail_path TEXT,
      duration REAL,
      resolution TEXT,
      bitrate INTEGER,
      fps INTEGER,
      file_size INTEGER,
      upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      user_id TEXT,
      FOREIGN KEY (user_id) REFERENCES users(id)
    )`);
    console.log('✓ Videos table created/verified');

    // 3. Streams table (with ALL columns)
    await runSQL(`CREATE TABLE IF NOT EXISTS streams (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      video_id TEXT,
      rtmp_url TEXT NOT NULL,
      stream_key TEXT NOT NULL,
      platform TEXT,
      platform_icon TEXT,
      bitrate INTEGER DEFAULT 2500,
      resolution TEXT,
      fps INTEGER DEFAULT 30,
      orientation TEXT DEFAULT 'horizontal',
      loop_video BOOLEAN DEFAULT 1,
      schedule_time TIMESTAMP,
      duration INTEGER,
      status TEXT DEFAULT 'offline',
      status_updated_at TIMESTAMP,
      start_time TIMESTAMP,
      end_time TIMESTAMP,
      use_advanced_settings BOOLEAN DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      user_id TEXT,
      FOREIGN KEY (user_id) REFERENCES users(id),
      FOREIGN KEY (video_id) REFERENCES videos(id)
    )`);
    console.log('✓ Streams table created/verified');

    // 4. Stream history table
    await runSQL(`CREATE TABLE IF NOT EXISTS stream_history (
      id TEXT PRIMARY KEY,
      stream_id TEXT,
      title TEXT NOT NULL,
      platform TEXT,
      platform_icon TEXT,
      video_id TEXT,
      video_title TEXT,
      resolution TEXT,
      bitrate INTEGER,
      fps INTEGER,
      start_time TIMESTAMP,
      end_time TIMESTAMP,
      duration INTEGER,
      use_advanced_settings BOOLEAN DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      user_id TEXT,
      FOREIGN KEY (user_id) REFERENCES users(id),
      FOREIGN KEY (stream_id) REFERENCES streams(id),
      FOREIGN KEY (video_id) REFERENCES videos(id)
    )`);
    console.log('✓ Stream history table created/verified');

    // 5. Subscription plans table
    await runSQL(`CREATE TABLE IF NOT EXISTS subscription_plans (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      price REAL NOT NULL,
      currency TEXT DEFAULT 'USD',
      billing_period TEXT DEFAULT 'monthly',
      max_streaming_slots INTEGER DEFAULT 1,
      max_storage_gb INTEGER DEFAULT 5,
      features TEXT,
      is_active BOOLEAN DEFAULT 1,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`);
    console.log('✓ Subscription plans table created/verified');

    // 6. User subscriptions table
    await runSQL(`CREATE TABLE IF NOT EXISTS user_subscriptions (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      plan_id TEXT NOT NULL,
      status TEXT DEFAULT 'active',
      start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      end_date TIMESTAMP,
      payment_method TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id),
      FOREIGN KEY (plan_id) REFERENCES subscription_plans(id)
    )`);
    console.log('✓ User subscriptions table created/verified');

    // Verify streams table has all required columns
    console.log('\nVerifying streams table columns...');
    const streamsColumns = await getTableInfo('streams');
    const requiredColumns = [
      'id', 'title', 'video_id', 'rtmp_url', 'stream_key', 'platform', 
      'platform_icon', 'bitrate', 'resolution', 'fps', 'orientation', 
      'loop_video', 'schedule_time', 'duration', 'status', 'status_updated_at',
      'start_time', 'end_time', 'use_advanced_settings', 'created_at', 
      'updated_at', 'user_id'
    ];

    console.log('Current streams table columns:');
    streamsColumns.forEach(col => {
      console.log(`  ✓ ${col.name}: ${col.type}`);
    });

    const missingColumns = requiredColumns.filter(col => 
      !streamsColumns.some(existing => existing.name === col)
    );

    if (missingColumns.length > 0) {
      console.log('\nMissing columns detected:', missingColumns);
      console.log('This should not happen with the CREATE TABLE IF NOT EXISTS approach.');
    } else {
      console.log('\n✅ All required columns are present in streams table');
    }

    // Show all tables
    const tables = await new Promise((resolve, reject) => {
      db.all("SELECT name FROM sqlite_master WHERE type='table'", [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows.map(row => row.name));
      });
    });

    console.log('\n📊 Database tables:');
    tables.forEach(table => {
      console.log(`  - ${table}`);
    });

    console.log('\n✅ Database initialization completed successfully!');

  } catch (error) {
    console.error('❌ Error initializing database:', error);
  } finally {
    db.close();
    console.log('Database connection closed');
  }
}

// Run initialization
initializeDatabase();
