const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Connect to database
const dbPath = path.join(__dirname, '..', 'streamflow.db');
const db = new sqlite3.Database(dbPath);

console.log('Checking database tables...');

// Function to check if table exists
function tableExists(tableName) {
  return new Promise((resolve, reject) => {
    db.get(
      "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
      [tableName],
      (err, row) => {
        if (err) reject(err);
        else resolve(!!row);
      }
    );
  });
}

// Function to create streams table
function createStreamsTable() {
  return new Promise((resolve, reject) => {
    const createTableSQL = `CREATE TABLE IF NOT EXISTS streams (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      video_id TEXT,
      rtmp_url TEXT NOT NULL,
      stream_key TEXT NOT NULL,
      platform TEXT,
      platform_icon TEXT,
      bitrate INTEGER DEFAULT 2500,
      resolution TEXT,
      fps INTEGER DEFAULT 30,
      orientation TEXT DEFAULT 'horizontal',
      loop_video BOOLEAN DEFAULT 1,
      schedule_time TIMESTAMP,
      duration INTEGER,
      status TEXT DEFAULT 'offline',
      status_updated_at TIMESTAMP,
      start_time TIMESTAMP,
      end_time TIMESTAMP,
      use_advanced_settings BOOLEAN DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      user_id TEXT,
      FOREIGN KEY (user_id) REFERENCES users(id),
      FOREIGN KEY (video_id) REFERENCES videos(id)
    )`;

    db.run(createTableSQL, function(err) {
      if (err) {
        console.error('❌ Error creating streams table:', err.message);
        reject(err);
      } else {
        console.log('✓ Streams table created successfully');
        resolve();
      }
    });
  });
}

// Function to create other essential tables
function createEssentialTables() {
  return new Promise((resolve, reject) => {
    const tables = [
      {
        name: 'users',
        sql: `CREATE TABLE IF NOT EXISTS users (
          id TEXT PRIMARY KEY,
          username TEXT UNIQUE NOT NULL,
          email TEXT UNIQUE,
          password TEXT NOT NULL,
          avatar_path TEXT,
          gdrive_api_key TEXT,
          role TEXT DEFAULT 'user',
          plan_type TEXT DEFAULT 'Free',
          max_streaming_slots INTEGER DEFAULT 1,
          max_storage_gb INTEGER DEFAULT 5,
          used_storage_gb REAL DEFAULT 0,
          subscription_start_date TIMESTAMP,
          subscription_end_date TIMESTAMP,
          is_active BOOLEAN DEFAULT 1,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`
      },
      {
        name: 'videos',
        sql: `CREATE TABLE IF NOT EXISTS videos (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          filepath TEXT NOT NULL,
          thumbnail_path TEXT,
          duration REAL,
          resolution TEXT,
          bitrate INTEGER,
          fps INTEGER,
          file_size INTEGER,
          upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          user_id TEXT,
          FOREIGN KEY (user_id) REFERENCES users(id)
        )`
      },
      {
        name: 'subscription_plans',
        sql: `CREATE TABLE IF NOT EXISTS subscription_plans (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          price REAL NOT NULL,
          currency TEXT DEFAULT 'USD',
          billing_period TEXT DEFAULT 'monthly',
          max_streaming_slots INTEGER DEFAULT 1,
          max_storage_gb INTEGER DEFAULT 5,
          features TEXT,
          is_active BOOLEAN DEFAULT 1,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`
      }
    ];

    let completed = 0;
    const total = tables.length;

    tables.forEach(table => {
      db.run(table.sql, function(err) {
        if (err) {
          console.error(`❌ Error creating ${table.name} table:`, err.message);
        } else {
          console.log(`✓ ${table.name} table created/verified`);
        }
        
        completed++;
        if (completed === total) {
          resolve();
        }
      });
    });
  });
}

async function checkAndCreateTables() {
  try {
    // Get all existing tables
    const existingTables = await new Promise((resolve, reject) => {
      db.all(
        "SELECT name FROM sqlite_master WHERE type='table'",
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows.map(row => row.name));
        }
      );
    });

    console.log('Existing tables:', existingTables);

    // Check if streams table exists
    const streamsExists = await tableExists('streams');
    console.log(`Streams table exists: ${streamsExists}`);

    if (!streamsExists) {
      console.log('\nCreating missing tables...');
      
      // Create essential tables first
      await createEssentialTables();
      
      // Create streams table
      await createStreamsTable();
    } else {
      console.log('✓ Streams table already exists');
      
      // Check columns in existing streams table
      const columns = await new Promise((resolve, reject) => {
        db.all('PRAGMA table_info(streams)', [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      console.log('\nCurrent streams table columns:');
      columns.forEach(col => {
        console.log(`  - ${col.name}: ${col.type}`);
      });

      // Check if duration column exists
      const durationExists = columns.some(col => col.name === 'duration');
      if (!durationExists) {
        console.log('\nAdding missing duration column...');
        await new Promise((resolve, reject) => {
          db.run('ALTER TABLE streams ADD COLUMN duration INTEGER', function(err) {
            if (err) {
              console.error('❌ Error adding duration column:', err.message);
              reject(err);
            } else {
              console.log('✓ Duration column added successfully');
              resolve();
            }
          });
        });
      } else {
        console.log('✓ Duration column already exists');
      }
    }

    // Show final table list
    const finalTables = await new Promise((resolve, reject) => {
      db.all(
        "SELECT name FROM sqlite_master WHERE type='table'",
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows.map(row => row.name));
        }
      );
    });

    console.log('\n✅ Final database tables:', finalTables);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    db.close();
    console.log('Database connection closed');
  }
}

// Run the check and create process
checkAndCreateTables();
